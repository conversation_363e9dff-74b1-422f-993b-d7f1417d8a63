import { Injectable } from '@angular/core';
import { _HttpClient } from '@delon/theme';
import { environment } from '@env/environment';
import { QueryFilerModel } from '@model';
import { visnamTaiKhoanKetNoiRouter } from '@util';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class VisnamTaiKhoanKetNoiService {
  constructor(private http: _HttpClient) {}

  getFilter(model: QueryFilerModel): Observable<any> {
    return this.http.post(environment.api.baseUrl + visnamTaiKhoanKetNoiRouter.getFilter, model);
  }

  delete(id: any): Observable<any> {
    return this.http.delete(environment.api.baseUrl + visnamTaiKhoanKetNoiRouter.delete + id);
  }

  create(model: any): Observable<any> {
    return this.http.post(environment.api.baseUrl + visnamTaiKhoanKetNoiRouter.create, model);
  }

  update(id: any, model: any): Observable<any> {
    return this.http.put(environment.api.baseUrl + visnamTaiKhoanKetNoiRouter.update + id, model);
  }

  getById(id: string): Observable<any> {
    return this.http.get(environment.api.baseUrl + visnamTaiKhoanKetNoiRouter.getById + id);
  }

  getCombobox(count?: number): Observable<any> {
    const url = count 
      ? `${environment.api.baseUrl}${visnamTaiKhoanKetNoiRouter.getCombobox}?count=${count}`
      : environment.api.baseUrl + visnamTaiKhoanKetNoiRouter.getCombobox;
    return this.http.get(url);
  }

  syncCertificates(id: any): Observable<any> {
    return this.http.post(environment.api.baseUrl + visnamTaiKhoanKetNoiRouter.syncCertificates + id, {});
  }
}
