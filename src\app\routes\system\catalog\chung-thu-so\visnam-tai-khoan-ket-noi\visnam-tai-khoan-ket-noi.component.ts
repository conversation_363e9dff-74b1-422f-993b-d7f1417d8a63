import { Component, EventEmitter, Inject, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { I18NService } from '@core';
import { ACLService } from '@delon/acl';
import { ALAIN_I18N_TOKEN } from '@delon/theme';
import { QueryFilerModel, GridModel, ButtonModel } from '@model';
import { BtnCellRenderComponent, StatusCellRenderComponent } from '@shared';
import {
  AG_GIRD_CELL_STYLE,
  EXCEL_STYLES_DEFAULT,
  LIST_STATUS,
  QUERY_FILTER_DEFAULT,
  OVERLAY_LOADING_TEMPLATE,
  OVERLAY_NOROW_TEMPLATE,
  FORM_TYPE,
  EVENT_TYPE,
  PAGE_SIZE_OPTION_DEFAULT,
  cleanForm
} from '@util';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzModalService } from 'ng-zorro-antd/modal';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { Subscription } from 'rxjs';
import { VisnamTaiKhoanKetNoiService } from 'src/app/services/api/visnam-tai-khoan-ket-noi.service';

@Component({
  selector: 'app-visnam-tai-khoan-ket-noi',
  templateUrl: './visnam-tai-khoan-ket-noi.component.html',
  styleUrls: ['./visnam-tai-khoan-ket-noi.component.less']
})
export class VisnamTaiKhoanKetNoiComponent implements OnInit {
  @Input() isVisible = false;
  @Output() readonly eventEmmit = new EventEmitter<any>();

  constructor(
    private fb: FormBuilder,
    private visnamTaiKhoanKetNoiApiService: VisnamTaiKhoanKetNoiService,
    private aclService: ACLService,
    private notification: NzNotificationService,
    private message: NzMessageService,
    private modalService: NzModalService,
    @Inject(ALAIN_I18N_TOKEN) private i18n: I18NService
  ) {
    this.columnDefs = [
      {
        field: 'index',
        headerName: 'STT',
        width: 80
      },
      { field: 'key', headerName: 'Key', minWidth: 200, flex: 2 },
      {
        field: 'secret',
        headerName: 'Secret',
        minWidth: 200,
        flex: 2,
        cellRenderer: (params: any) => {
          if (params.value) {
            return `***${params.value.slice(-4)}`;
          }
          return '';
        }
      },
      {
        field: 'isActive',
        headerName: 'Trạng thái',
        minWidth: 120,
        flex: 1,
        cellRenderer: 'statusCellRender'
      },
      {
        headerName: 'Thao tác',
        minWidth: 200,
        pinned: 'right',
        cellRenderer: 'btnCellRender',
        cellRendererParams: {
          infoClicked: (item: any) => this.onViewItem(item),
          editClicked: (item: any) => this.onEditItem(item),
          deleteClicked: (item: any) => this.onDeleteItem(item),
          syncDataClicked: (item: any) => this.onSyncCertificates(item)
        }
      }
    ];

    this.defaultColDef = {
      minWidth: 100,
      cellStyle: AG_GIRD_CELL_STYLE,
      resizable: true
    };
    this.frameworkComponents = {
      btnCellRender: BtnCellRenderComponent,
      statusCellRender: StatusCellRenderComponent
    };

    this.excelStyles = EXCEL_STYLES_DEFAULT;
    this.pageSizeOptions = PAGE_SIZE_OPTION_DEFAULT;

    this.form = this.fb.group({
      key: [null, [Validators.required, Validators.maxLength(500)]],
      secret: [null, [Validators.required, Validators.maxLength(500)]]
    });

    //#region Init button
    this.btnAdd = {
      title: 'Thêm mới',
      visible: true,
      enable: true,
      grandAccess: true,
      click: ($event: any) => {
        this.onAddItem();
      }
    };
    this.btnDelete = {
      title: 'Xóa',
      visible: true,
      enable: false,
      grandAccess: true,
      click: ($event: any) => {
        this.onDeleteItem();
      }
    };
    this.btnSave = {
      title: 'Lưu',
      visible: true,
      enable: true,
      grandAccess: true,
      click: ($event: any) => {
        this.save();
      }
    };
    this.btnCancel = {
      title: 'Hủy',
      visible: true,
      enable: true,
      grandAccess: true,
      click: ($event: any) => {
        this.cancelForm();
      }
    };
    this.btnClose = {
      title: 'Đóng',
      visible: true,
      enable: true,
      grandAccess: true,
      click: ($event: any) => {
        this.handleClose();
      }
    };
    //#endregion Init button
  }

  isRenderComplete = false;
  isShowForm = false;
  isFormAdd = false;
  isFormEdit = false;
  isLoading = false;
  currentItem: any = {};

  form: FormGroup;
  listStatus = LIST_STATUS;
  filter: QueryFilerModel = { ...QUERY_FILTER_DEFAULT };
  pageSizeOptions: any[] = [];

  columnDefs: any[] = [];
  grid: GridModel = {
    dataCount: 0,
    rowData: [],
    totalData: 0
  };
  private gridApi: any;
  private gridColumnApi: any;
  defaultColDef: any;
  rowSelection = 'multiple';
  overlayLoadingTemplate = OVERLAY_LOADING_TEMPLATE;
  overlayNoRowsTemplate = OVERLAY_NOROW_TEMPLATE;
  excelStyles: any;
  frameworkComponents: any;

  btnAdd: ButtonModel;
  btnDelete: ButtonModel;
  btnSave: ButtonModel;
  btnCancel: ButtonModel;
  btnClose: ButtonModel;

  title = 'Quản lý tài khoản kết nối Visnam';

  ngOnInit(): void {
    this.initRightOfUser();
    this.isRenderComplete = true;
  }

  initRightOfUser() {
    // Implement permission logic here if needed
  }

  //#region Ag-grid
  onGridReady(params: any): void {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.initGridData();
  }

  onSelectionChanged(event: any): void {
    const selectedRows = this.gridApi.getSelectedRows();
    this.btnDelete.enable = selectedRows.length > 0;
  }

  onCellDoubleClicked(event: any): void {
    this.onViewItem(event.data);
  }

  onPageNumberChange(): void {
    this.initGridData();
  }

  onPageSizeChange(): void {
    this.initGridData();
  }

  initGridData(): void {
    this.isLoading = true;
    const subscription: Subscription = this.visnamTaiKhoanKetNoiApiService.getFilter(this.filter).subscribe({
      next: (res: any) => {
        this.isLoading = false;
        if (res.data === null || res.data === undefined) {
          this.notification.error(this.i18n.fanyi('app.common.error.title'), `${res.message}`);
          return;
        }

        const dataResult = res.data;

        let i = (this.filter.pageSize ?? 0) * ((this.filter.pageNumber ?? 0) - 1);

        for (const item of dataResult.data) {
          item.index = ++i;
          item.deleteGrantAccess = true;
          item.syncDataGrantAccess = true;
        }
        // this.grid.totalData = dataResult.totalCount;
        // this.grid.dataCount = dataResult.dataCount;
        this.grid.rowData = dataResult.data;
        // this.pageSizeOptions = [...PAGE_SIZE_OPTION_DEFAULT];
        // // tslint:disable-next-line: variable-name
        // this.pageSizeOptions = this.pageSizeOptions.filter(number => {
        //   return number < dataResult.totalCount;
        // });
        // this.pageSizeOptions.push(dataResult.totalCount);
      },
      error: (err: any) => {
        this.message.error('Có lỗi xảy ra khi tải dữ liệu');
        this.isLoading = false;
      }
    });
  }
  //#endregion Ag-grid

  handleClose(): void {
    this.isVisible = false;
    this.eventEmmit.emit({ type: EVENT_TYPE.CLOSE });
  }

  onAddItem(): void {
    this.isShowForm = true;
    this.isFormAdd = true;
    this.isFormEdit = false;
    this.currentItem = {};
    this.resetForm();
  }

  onEditItem(item: any = null): void {
    if (item === null) {
      const selectedRows = this.gridApi.getSelectedRows();
      item = selectedRows[0];
    }
    this.isShowForm = true;
    this.isFormAdd = false;
    this.isFormEdit = true;
    this.currentItem = item;
    this.updateDataToForm(item);
  }

  onViewItem(item: any = null): void {
    if (item === null) {
      const selectedRows = this.gridApi.getSelectedRows();
      item = selectedRows[0];
    }
    this.onEditItem(item);
  }

  onDeleteItem(item: any = null): void {
    let selectedRows: any[] = [];
    if (item === null) {
      selectedRows = this.gridApi.getSelectedRows();
    } else {
      selectedRows = [item];
    }

    if (selectedRows.length === 0) {
      this.message.warning('Vui lòng chọn ít nhất một bản ghi để xóa');
      return;
    }

    this.modalService.confirm({
      nzTitle: 'Xác nhận xóa',
      nzContent: `Bạn có chắc chắn muốn xóa ${selectedRows.length} tài khoản kết nối đã chọn?`,
      nzOkText: 'Xóa',
      nzOkType: 'primary',
      nzOkDanger: true,
      nzOnOk: () => this.confirmDelete(selectedRows),
      nzCancelText: 'Hủy'
    });
  }

  confirmDelete(items: any[]): void {
    const deletePromises = items.map(item => this.visnamTaiKhoanKetNoiApiService.delete(item.id).toPromise());

    Promise.all(deletePromises).then(
      (results: any[]) => {
        const successCount = results.filter(res => res.data).length;
        const failCount = results.length - successCount;

        if (failCount === 0) {
          this.message.success(`Xóa thành công ${successCount} tài khoản kết nối`);
        } else {
          this.message.warning(`Xóa thành công ${successCount}, thất bại ${failCount} tài khoản kết nối`);
        }
        this.initGridData();
      },
      (error: any) => {
        this.message.error('Có lỗi xảy ra khi xóa dữ liệu');
      }
    );
  }

  onSyncCertificates(item: any): void {
    this.modalService.confirm({
      nzTitle: 'Xác nhận đồng bộ',
      nzContent: `Bạn có chắc chắn muốn đồng bộ chứng thư số từ tài khoản "${item.userName}"?`,
      nzOkText: 'Đồng bộ',
      nzOkType: 'primary',
      nzOnOk: () => this.confirmSyncCertificates(item),
      nzCancelText: 'Hủy'
    });
  }

  confirmSyncCertificates(item: any): void {
    this.isLoading = true;
    this.visnamTaiKhoanKetNoiApiService.syncCertificates(item.id).subscribe({
      next: (res: any) => {
        this.isLoading = false;
        if (res.data) {
          this.message.success(res.data.message);
          // this.eventEmmit.emit({ type: EVENT_TYPE.SUCCESS });
        } else {
          this.message.error(res.message || 'Có lỗi xảy ra khi đồng bộ');
        }
      },
      error: (err: any) => {
        this.isLoading = false;
        this.message.error('Có lỗi xảy ra khi đồng bộ chứng thư số');
      }
    });
  }

  resetForm(): void {
    this.form.reset();
  }

  updateDataToForm(data: any): void {
    this.form.patchValue({
      key: data.key,
      secret: data.secret
    });
  }

  cancelForm(): void {
    this.isShowForm = false;
    this.resetForm();
  }

  save(): void {
    this.isLoading = true;

    cleanForm(this.form);
    for (const i in this.form.controls) {
      this.form.controls[i].markAsDirty();
      this.form.controls[i].updateValueAndValidity();
    }
    if (!this.form.valid) {
      this.isLoading = false;
      this.message.error('Vui lòng kiểm tra lại thông tin nhập vào');
      return;
    }

    const formValue = this.form.value;
    const data = {
      key: formValue.key,
      secret: formValue.secret
    };

    if (this.isFormAdd) {
      this.visnamTaiKhoanKetNoiApiService.create(data).subscribe({
        next: (res: any) => {
          this.isLoading = false;
          if (res.data === null || res.data === undefined) {
            this.message.error(`${res.message}`);
            return;
          }
          this.message.success('Thêm mới tài khoản kết nối thành công');

          this.cancelForm();
          this.initGridData();
        },
        error: (err: any) => {
          this.isLoading = false;
          this.message.error('Có lỗi xảy ra khi thêm mới tài khoản kết nối');
        }
      });
    } else if (this.isFormEdit) {
      this.visnamTaiKhoanKetNoiApiService.update(this.currentItem.id, data).subscribe({
        next: (res: any) => {
          this.isLoading = false;
          if (res.data === null || res.data === undefined) {
            this.message.error(`${res.message}`);
            return;
          }
          this.message.success('Cập nhật tài khoản kết nối thành công');
          this.cancelForm();
          this.initGridData();
        },
        error: (err: any) => {
          this.isLoading = false;
          this.message.error('Có lỗi xảy ra khi cập nhật tài khoản kết nối');
        }
      });
    }
  }
}
