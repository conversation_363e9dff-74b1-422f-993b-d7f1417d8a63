/* API Key Item component styles */
.api-key-item-modal {
  .ant-form-item {
    margin-bottom: 16px;
  }
  
  .ant-form-item-label {
    text-align: left;
  }
  
  .ant-input-number {
    width: 100%;
  }
  
  .ant-picker {
    width: 100%;
  }
  
  .ant-switch {
    margin-top: 4px;
  }
}

.readonly-field {
  background-color: #f5f5f5;
  cursor: not-allowed;
}

.form-explain {
  color: #999;
  font-size: 12px;
  margin-top: 4px;
}
