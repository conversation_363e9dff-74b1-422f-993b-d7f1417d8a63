<nz-modal
  [(nzVisible)]="isVisible"
  [nzTitle]="modalTitle"
  [nzContent]="modalContent"
  [nzFooter]="modalFooter"
  nzMaskClosable="false"
  nzWidth="1000px"
  (nzOnCancel)="handleCancel()"
>
  <ng-template #modalTitle> {{ tittle }} </ng-template>

  <ng-template #modalContent>
    <form nz-form [formGroup]="form" (ngSubmit)="save()">
      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="userId">{{ 'function.api-key.modal.form.user' | i18n }}</nz-form-label>
        <nz-form-control [nzSm]="17" [nzXs]="24" nzErrorTip="{{ 'function.api-key.modal.form.user.required' | i18n }}">
          <nz-select
            formControlName="userId"
            id="userId"
            placeholder="{{ 'function.api-key.modal.form.user.placeholder' | i18n }}"
            nzShowSearch
            nzAllowClear
            style="width: 100%"
          >
            <nz-option *ngFor="let user of userList" [nzValue]="user.userId" [nzLabel]="user.userName"></nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24" nzFor="key">{{ 'function.api-key.modal.form.key' | i18n }}</nz-form-label>
        <nz-form-control [nzSm]="17" [nzXs]="24">
          <input
            nz-input
            formControlName="key"
            id="key"
            placeholder="{{ 'function.api-key.modal.form.key.placeholder' | i18n }}"
            [readonly]="true"
          />
          <nz-form-explain *ngIf="isAdd">{{ 'function.api-key.modal.form.key.note' | i18n }}</nz-form-explain>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="validFrom">{{
          'function.api-key.modal.form.valid-from' | i18n
        }}</nz-form-label>
        <nz-form-control [nzSm]="17" [nzXs]="24" nzErrorTip="{{ 'function.api-key.modal.form.valid-from.required' | i18n }}">
          <nz-date-picker
            formControlName="validFrom"
            id="validFrom"
            placeholder="{{ 'function.api-key.modal.form.valid-from.placeholder' | i18n }}"
            style="width: 100%"
            nzFormat="dd/MM/yyyy"
          ></nz-date-picker>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="validTo">{{ 'function.api-key.modal.form.valid-to' | i18n }}</nz-form-label>
        <nz-form-control [nzSm]="17" [nzXs]="24" nzErrorTip="{{ 'function.api-key.modal.form.valid-to.required' | i18n }}">
          <nz-date-picker
            formControlName="validTo"
            id="validTo"
            placeholder="{{ 'function.api-key.modal.form.valid-to.placeholder' | i18n }}"
            style="width: 100%"
            nzFormat="dd/MM/yyyy"
          ></nz-date-picker>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24" nzFor="isActive">{{ 'function.api-key.modal.form.status' | i18n }}</nz-form-label>
        <nz-form-control [nzSm]="17" [nzXs]="24">
          <label nz-checkbox formControlName="isActive" id="isActive"></label>
        </nz-form-control>
      </nz-form-item>

      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24" nzFor="description">{{ 'function.api-key.modal.form.description' | i18n }}</nz-form-label>
        <nz-form-control [nzSm]="17" [nzXs]="24">
          <textarea
            nz-input
            formControlName="description"
            id="description"
            placeholder="{{ 'function.api-key.modal.form.description.placeholder' | i18n }}"
            [nzAutosize]="{ minRows: 3, maxRows: 6 }"
          ></textarea>
        </nz-form-control>
      </nz-form-item>
    </form>
  </ng-template>

  <ng-template #modalFooter>
    <button
      nz-button
      nzType="default"
      (click)="btnCancel.click($event)"
      [nzLoading]="isLoading"
      *ngIf="btnCancel.visible && btnCancel.grandAccess"
    >
      {{ btnCancel.title }}
    </button>
    <button
      nz-button
      nzType="primary"
      (click)="btnEdit.click($event)"
      [nzLoading]="isLoading"
      *ngIf="isInfo && btnEdit.visible && btnEdit.grandAccess"
    >
      {{ btnEdit.title }}
    </button>
    <button
      nz-button
      nzType="primary"
      (click)="btnSave.click($event)"
      [nzLoading]="isLoading"
      *ngIf="(isAdd || isEdit) && btnSave.visible && btnSave.grandAccess"
    >
      {{ btnSave.title }}
    </button>
  </ng-template>
</nz-modal>
