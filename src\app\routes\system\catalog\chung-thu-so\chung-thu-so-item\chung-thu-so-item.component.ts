import { Component, EventEmitter, Inject, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { I18NService } from '@core';
import { ACLService } from '@delon/acl';
import { ALAIN_I18N_TOKEN } from '@delon/theme';
import { log } from '@delon/util';
import { ButtonModel } from '@model';
import { cleanForm, EVENT_TYPE, FORM_TYPE } from '@util';
import { NzMessageService } from 'ng-zorro-antd/message';
import { Subscription } from 'rxjs';
import { ChungThuSoService } from 'src/app/services/api/chung-thu-so.service';

@Component({
  selector: 'app-chung-thu-so-item',
  templateUrl: './chung-thu-so-item.component.html',
  styleUrls: ['./chung-thu-so-item.component.less']
})
export class ChungThuSoItemComponent implements OnInit {
  @Input() type = 'add';
  @Input() item: any;
  @Input() isVisible = false;
  @Input() option: any;
  @Output() readonly eventEmmit = new EventEmitter<any>();

  form: FormGroup;

  isInfo = false;
  isEdit = false;
  isAdd = false;
  tittle = '';

  isLoading = false;

  btnSave: ButtonModel;
  btnCancel: ButtonModel;
  btnEdit: ButtonModel;

  constructor(
    private fb: FormBuilder,
    private messageService: NzMessageService,
    private chungThuSoApiService: ChungThuSoService,
    @Inject(ALAIN_I18N_TOKEN) private i18n: I18NService,
    private aclService: ACLService
  ) {
    this.btnSave = {
      title: 'Lưu',
      visible: true,
      enable: true,
      grandAccess: true,
      click: ($event: any) => {
        this.save();
      }
    };
    this.btnCancel = {
      title: 'Đóng',
      visible: true,
      enable: true,
      grandAccess: true,
      click: ($event: any) => {
        this.handleCancel();
      }
    };
    this.btnEdit = {
      title: 'Chỉnh sửa',
      visible: true,
      enable: true,
      grandAccess: true,
      click: ($event: any) => {
        this.updateFormToEdit();
      }
    };
    this.form = this.fb.group({
      serialNumber: [null, [Validators.required, Validators.maxLength(100)]],
      subjectName: [null, [Validators.required, Validators.maxLength(500)]],
      issuer: [null, [Validators.required, Validators.maxLength(50)]],
      certificateBase64: [null, [Validators.required]],
      notBefore: [null],
      notAfter: [null],
      source: [0],
      isActive: [true],
      order: [1],
      userId: [null],
      referenceId: [null]
    });
  }

  handleCancel(): void {
    this.isVisible = false;
    this.eventEmmit.emit({ type: EVENT_TYPE.CLOSE });
  }

  ngOnInit(): void {
    this.initRightOfUser();
  }

  initRightOfUser(): void {
    // Implement permission logic here if needed
    // this.btnSave.grandAccess = this.aclService.canAbility('CHUNG_THU_SO_ADD') || this.aclService.canAbility('CHUNG_THU_SO_EDIT');
    // this.btnEdit.grandAccess = this.aclService.canAbility('CHUNG_THU_SO_EDIT');
  }

  //#region Update-form-type
  updateFormToAdd(): void {
    // Không cho phép thêm mới chứng thư số, chỉ đồng bộ qua Visnam
    this.isInfo = true; // Chuyển sang chế độ xem thông tin
    this.isEdit = false;
    this.isAdd = false;
    this.tittle = 'Thông tin chứng thư số';
    this.item = {};
    this.disableAllFormControls();
  }

  updateFormToInfo(): void {
    this.isInfo = true;
    this.isEdit = false;
    this.isAdd = false;
    this.tittle = 'Thông tin chứng thư số';
    this.disableAllFormControls();
  }

  updateFormToEdit(): void {
    this.isInfo = false;
    this.isEdit = true;
    this.isAdd = false;
    this.tittle = 'Chỉnh sửa chứng thư số';
    this.enableAllFormControls();
  }

  enableAllFormControls(): void {
    // Chỉ cho phép chỉnh sửa trường isActive
    Object.keys(this.form.controls).forEach(key => {
      if (key === 'isActive') {
        this.form.get(key)?.enable();
      } else {
        this.form.get(key)?.disable();
      }
    });
  }

  disableAllFormControls(): void {
    Object.keys(this.form.controls).forEach(key => {
      this.form.get(key)?.disable();
    });
  }

  resetForm(): void {
    this.form.reset();
    this.form.get('isActive')?.setValue(true);
    this.form.get('order')?.setValue(1);
    this.form.get('source')?.setValue(0);
  }

  updateDataToForm(data: any): void {
    this.form.patchValue({
      serialNumber: data.serialNumber,
      subjectName: data.subjectName,
      issuer: data.issuer,
      certificateBase64: data.certificateBase64,
      notBefore: data.notBefore ? new Date(data.notBefore) : null,
      notAfter: data.notAfter ? new Date(data.notAfter) : null,
      source: data.source || 0,
      isActive: data.isActive !== undefined ? data.isActive : true,
      order: data.order || 1,
      userId: data.userId,
      referenceId: data.referenceId
    });
  }

  //#endregion Update-form-type

  public initData(data: any, type: any = null, option: any = {}): void {
    this.resetForm();
    this.isLoading = false;
    this.item = data;
    this.type = type;
    this.option = option;

    if (this.item?.id) {
      this.getDataInfo(this.item.id);
    }
    switch (type) {
      case FORM_TYPE.ADD:
        this.updateFormToAdd();
        break;
      case FORM_TYPE.INFO:
        this.updateFormToInfo();
        break;
      case FORM_TYPE.EDIT:
        this.updateFormToEdit();
        break;
      default:
        this.updateFormToAdd();
        break;
    }
  }

  closeModalReloadData(): void {
    this.isVisible = false;
    this.eventEmmit.emit({ type: EVENT_TYPE.SUCCESS });
  }

  getDataInfo(id: any): Subscription | undefined {
    this.isLoading = true;
    const rs = this.chungThuSoApiService.getById(id).subscribe({
      next: (res: any) => {
        this.isLoading = false;
        if (res.data === null || res.data === undefined) {
          this.messageService.error(`${res.message}`);
          return;
        }
        this.item = res.data;
        this.updateDataToForm(res.data);
      },
      error: (err: any) => {
        this.isLoading = false;
        this.messageService.error('Có lỗi xảy ra khi tải dữ liệu');
      },
      complete: () => {
        this.isLoading = false;
      }
    });
    return rs;
  }

  onCertificateFileChange(event: any): void {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e: any) => {
        const base64String = e.target.result.split(',')[1]; // Remove data:*/*;base64, prefix
        this.form.get('certificateBase64')?.setValue(base64String);
      };
      reader.readAsDataURL(file);
    }
  }

  save(): Subscription | undefined {
    this.isLoading = true;

    cleanForm(this.form);
    for (const i in this.form.controls) {
      this.form.controls[i].markAsDirty();
      this.form.controls[i].updateValueAndValidity();
    }
    if (!this.form.valid) {
      this.isLoading = false;
      this.messageService.error('Vui lòng kiểm tra lại thông tin nhập vào');
      return;
    }

    const formValue = this.form.value;
    // Chỉ cho phép cập nhật trường isActive
    const data = {
      id: this.item.id,
      serialNumber: this.item.serialNumber, // Giữ nguyên giá trị cũ
      subjectName: this.item.subjectName, // Giữ nguyên giá trị cũ
      issuer: this.item.issuer, // Giữ nguyên giá trị cũ
      certificateBase64: this.item.certificateBase64, // Giữ nguyên giá trị cũ
      notBefore: this.item.notBefore, // Giữ nguyên giá trị cũ
      notAfter: this.item.notAfter, // Giữ nguyên giá trị cũ
      source: this.item.source || 0, // Giữ nguyên giá trị cũ
      isActive: formValue.isActive, // Chỉ cho phép thay đổi trường này
      order: this.item.order || 1, // Giữ nguyên giá trị cũ
      userId: this.item.userId, // Giữ nguyên giá trị cũ
      referenceId: this.item.referenceId // Giữ nguyên giá trị cũ
    };

    if (this.isAdd) {
      // Không cho phép thêm mới chứng thư số
      this.isLoading = false;
      this.messageService.warning('Không thể thêm mới chứng thư số. Vui lòng sử dụng chức năng đồng bộ từ Visnam.');
      return;
    } else if (this.isEdit) {
      const promise = this.chungThuSoApiService.update(this.item.id, data).subscribe({
        next: (res: any) => {
          this.isLoading = false;
          if (res.data === null || res.data === undefined) {
            this.messageService.error(`${res.message}`);
            return;
          }
          this.messageService.success('Cập nhật chứng thư số thành công');
          this.closeModalReloadData();
        },
        error: (err: any) => {
          this.isLoading = false;
          if (err.error) {
            this.messageService.error(`${err.error.message}`);
          } else {
            this.messageService.error('Có lỗi xảy ra khi cập nhật');
          }
        },
        complete: () => (this.isLoading = false)
      });
      return promise;
    } else {
      return;
    }
  }
}
