import { Injectable } from '@angular/core';
import { _HttpClient } from '@delon/theme';
import { environment } from '@env/environment';
import { QueryFilerModel } from '@model';
import { apiKeyRouter } from '@util';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ApiKeyService {
  constructor(private http: _HttpClient) {}

  getFilter(model: QueryFilerModel): Observable<any> {
    return this.http.post(environment.api.baseUrl + apiKeyRouter.getFilter, model);
  }

  delete(id: any): Observable<any> {
    return this.http.delete(environment.api.baseUrl + apiKeyRouter.delete + id);
  }

  create(model: any): Observable<any> {
    return this.http.post(environment.api.baseUrl + apiKeyRouter.create, model);
  }

  update(id: any, model: any): Observable<any> {
    return this.http.put(environment.api.baseUrl + apiKeyRouter.update + id, model);
  }

  getById(id: string): Observable<any> {
    return this.http.get(environment.api.baseUrl + apiKeyRouter.getById + id);
  }

  getCombobox(): Observable<any> {
    return this.http.get(environment.api.baseUrl + apiKeyRouter.getCombobox);
  }

  revoke(id: any): Observable<any> {
    return this.http.put(environment.api.baseUrl + apiKeyRouter.revoke + id + apiKeyRouter.revokeAction, {});
  }
}
