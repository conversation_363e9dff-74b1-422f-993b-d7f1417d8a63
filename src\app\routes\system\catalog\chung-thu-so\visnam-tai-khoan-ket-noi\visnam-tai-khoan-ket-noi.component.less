/* Component specific styles for VisnamTaiKhoanKetNoi */
.padding-bottom-10 {
  padding-bottom: 10px;
}

.margin-bottom-16 {
  margin-bottom: 16px;
}

.margin-top-16 {
  margin-top: 16px;
}

.pull-right {
  text-align: right;
}

.btn-primary {
  margin-left: 8px;
}

.btn-secondary {
  margin-left: 8px;
}

.btn-danger {
  margin-left: 8px;
}

.btn-warning {
  margin-left: 8px;
}

#visnam-tai-khoan-ket-noi-grid {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
}

.ag-theme-alpine {
  --ag-header-height: 40px;
  --ag-row-height: 40px;
}

/* Modal content spacing */
.ant-modal-body {
  max-height: 80vh;
  overflow-y: auto;
}

/* Form item spacing */
nz-form-item {
  margin-bottom: 16px;
}

/* Switch styling */
nz-switch {
  margin-right: 8px;
}

/* Custom button styles for sync action */
.sync-button {
  background-color: #52c41a;
  border-color: #52c41a;
  color: white;
}

.sync-button:hover {
  background-color: #73d13d;
  border-color: #73d13d;
}

/* Secret field masking */
.secret-masked {
  font-family: monospace;
  letter-spacing: 2px;
}

/* Grid action buttons */
.ag-cell-action-buttons {
  display: flex;
  gap: 4px;
  align-items: center;
  justify-content: center;
}

.ag-cell-action-buttons button {
  padding: 2px 6px;
  font-size: 12px;
  height: 24px;
  min-width: 60px;
}
