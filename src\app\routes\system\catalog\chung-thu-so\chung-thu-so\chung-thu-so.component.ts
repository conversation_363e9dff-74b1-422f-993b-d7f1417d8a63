import { Component, ElementRef, Inject, OnInit, ViewChild } from '@angular/core';
import { I18NService } from '@core';
import { ACLService } from '@delon/acl';
import { ALAIN_I18N_TOKEN } from '@delon/theme';
import { QueryFilerModel, GridModel, ButtonModel } from '@model';
import { BtnCellRenderComponent, DateCellRenderComponent, StatusCellRenderComponent } from '@shared';
import {
  AG_GIRD_CELL_STYLE,
  EXCEL_STYLES_DEFAULT,
  LIST_STATUS,
  QUERY_FILTER_DEFAULT,
  OVERLAY_LOADING_TEMPLATE,
  OVERLAY_NOROW_TEMPLATE,
  FORM_TYPE,
  EVENT_TYPE,
  PAGE_SIZE_OPTION_DEFAULT
} from '@util';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzModalService } from 'ng-zorro-antd/modal';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { Subscription } from 'rxjs';
import { ChungThuSoService } from 'src/app/services/api/chung-thu-so.service';

import { ChungThuSoItemComponent } from '../chung-thu-so-item/chung-thu-so-item.component';

@Component({
  selector: 'app-chung-thu-so',
  templateUrl: './chung-thu-so.component.html',
  styleUrls: ['./chung-thu-so.component.less']
})
export class ChungThuSoComponent implements OnInit {
  constructor(
    private chungThuSoApiService: ChungThuSoService,
    private aclService: ACLService,
    private notification: NzNotificationService,
    private message: NzMessageService,
    private modalService: NzModalService,
    @Inject(ALAIN_I18N_TOKEN) private i18n: I18NService,
    private elementRef: ElementRef
  ) {
    this.columnDefs = [
      {
        field: 'index',
        headerName: 'STT',
        width: 100
      },
      { field: 'serialNumber', headerName: 'Số serial', minWidth: 180, flex: 1 },
      { field: 'subjectName', headerName: 'Tên chủ thể', minWidth: 250, flex: 2 },
      { field: 'issuer', headerName: 'Nhà phát hành', minWidth: 180, flex: 1 },
      {
        field: 'notBefore',
        headerName: 'Có hiệu lực từ',
        minWidth: 150,
        flex: 1,
        cellRenderer: 'dateCellRender'
      },
      {
        field: 'notAfter',
        headerName: 'Có hiệu lực đến',
        minWidth: 150,
        flex: 1,
        cellRenderer: 'dateCellRender'
      },
      {
        field: 'isActive',
        headerName: 'Trạng thái',
        minWidth: 120,
        flex: 1,
        cellRenderer: 'statusCellRender'
      },
      {
        headerName: 'Thao tác',
        minWidth: 150,
        pinned: 'right',
        cellRenderer: 'btnCellRender',
        cellRendererParams: {
          infoClicked: (item: any) => this.onViewItem(item),
          editClicked: (item: any) => this.onEditItem(item),
          deleteClicked: (item: any) => this.onDeleteItem(item)
        }
      }
    ];
    this.defaultColDef = {
      minWidth: 100,
      cellStyle: AG_GIRD_CELL_STYLE,
      resizable: true
    };
    this.frameworkComponents = {
      btnCellRender: BtnCellRenderComponent,
      statusCellRender: StatusCellRenderComponent,
      dateCellRender: DateCellRenderComponent
    };

    this.excelStyles = EXCEL_STYLES_DEFAULT;
    this.pageSizeOptions = PAGE_SIZE_OPTION_DEFAULT;

    //#region Init button
    this.btnAdd = {
      title: 'Thêm mới',
      visible: false, // Ẩn nút thêm mới, chỉ cho phép đồng bộ qua Visnam
      enable: false,
      grandAccess: false,
      click: ($event: any) => {
        this.onAddItem();
      }
    };
    this.btnDelete = {
      title: 'Xóa',
      visible: true,
      enable: false,
      grandAccess: true,
      click: ($event: any) => {
        this.onDeleteItem();
      }
    };
    this.btnSearch = {
      title: 'Tìm kiếm',
      visible: true,
      enable: true,
      grandAccess: true,
      click: ($event: any) => {
        this.initGridData();
      }
    };
    this.btnResetSearch = {
      title: 'Đặt lại',
      visible: true,
      enable: true,
      grandAccess: true,
      click: ($event: any) => {
        this.onResetSearch(false);
      }
    };
    this.btnReload = {
      title: 'Tải lại',
      visible: true,
      enable: true,
      grandAccess: true,
      click: ($event: any) => {
        this.onResetSearch(true);
      }
    };
    //#endregion Init button
  }
  @ViewChild(ChungThuSoItemComponent, { static: false }) itemModal!: { initData: (arg0: {}, arg1: string, option: any) => void };

  isRenderComplete = false;

  listStatus = LIST_STATUS;
  filter: QueryFilerModel = { ...QUERY_FILTER_DEFAULT };
  pageSizeOptions: any[] = [];

  columnDefs: any[] = [];
  grid: GridModel = {
    dataCount: 0,
    rowData: [],
    totalData: 0
  };
  private gridApi: any;
  private gridColumnApi: any;
  defaultColDef: any;
  rowSelection = 'multiple';
  overlayLoadingTemplate = OVERLAY_LOADING_TEMPLATE;
  overlayNoRowsTemplate = OVERLAY_NOROW_TEMPLATE;
  quickText = '';
  excelStyles: any;
  frameworkComponents: any;

  btnAdd: ButtonModel;
  btnDelete: ButtonModel;
  btnResetSearch: ButtonModel;
  btnSearch: ButtonModel;
  btnReload: ButtonModel;

  isLoading = false;
  isShowDelete = false;
  isShowImport = false;

  title = 'Quản lý chứng thư số';

  modal: any = {
    type: '',
    item: {},
    isShow: false,
    option: {}
  };

  visnamAccountModalVisible = false;

  ngOnInit(): void {
    this.initRightOfUser();
    this.isRenderComplete = true;
  }

  initRightOfUser() {
    // Implement permission logic here if needed
  }

  //#region Ag-grid
  onGridReady(params: any): void {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.initGridData();
  }

  onSelectionChanged(event: any): void {
    const selectedRows = this.gridApi.getSelectedRows();
    this.btnDelete.enable = selectedRows.length > 0;
  }

  onCellDoubleClicked(event: any): void {
    this.onViewItem(event.data);
  }

  onPageNumberChange(): void {
    this.initGridData();
  }

  onPageSizeChange(): void {
    this.initGridData();
  }

  initGridData(): void {
    this.isLoading = true;
    const subscription: Subscription = this.chungThuSoApiService.getFilter(this.filter).subscribe({
      next: (res: any) => {
        this.isLoading = false;
        if (res.data === null || res.data === undefined) {
          this.notification.error(this.i18n.fanyi('app.common.error.title'), `${res.message}`);
          return;
        }

        const dataResult = res.data;

        let i = (this.filter.pageSize ?? 0) * ((this.filter.pageNumber ?? 0) - 1);

        for (const item of dataResult.data) {
          item.index = ++i;
          item.deleteGrantAccess = true;
        }
        this.grid.totalData = dataResult.totalCount;
        this.grid.dataCount = dataResult.dataCount;
        this.grid.rowData = dataResult.data;
        this.pageSizeOptions = [...PAGE_SIZE_OPTION_DEFAULT];
        // tslint:disable-next-line: variable-name
        this.pageSizeOptions = this.pageSizeOptions.filter(number => {
          return number < dataResult.totalCount;
        });
        this.pageSizeOptions.push(dataResult.totalCount);
      },
      error: (err: any) => {
        this.message.error('Có lỗi xảy ra khi tải dữ liệu');
        this.isLoading = false;
      }
    });
  }
  //#endregion Ag-grid

  //#region Event
  onResetSearch(reloadData: boolean): void {
    this.filter.pageNumber = 1;
    this.filter.textSearch = undefined;
    this.filter['status'] = undefined;
    if (reloadData) {
      this.initGridData();
    }
  }

  onAddItem(): void {
    this.modal = {
      type: FORM_TYPE.ADD,
      item: {},
      isShow: true,
      option: {}
    };
    this.itemModal.initData({}, FORM_TYPE.ADD, {});
  }

  onEditItem(item: any = null): void {
    if (item === null) {
      const selectedRows = this.gridApi.getSelectedRows();
      item = selectedRows[0];
    }
    this.modal = {
      type: FORM_TYPE.EDIT,
      item,
      isShow: true,
      option: {}
    };
    this.itemModal.initData(item, FORM_TYPE.EDIT, {});
  }

  onViewItem(item: any = null): void {
    if (item === null) {
      const selectedRows = this.gridApi.getSelectedRows();
      item = selectedRows[0];
    }
    this.modal = {
      type: FORM_TYPE.INFO,
      item,
      isShow: true,
      option: {}
    };
    this.itemModal.initData(item, FORM_TYPE.INFO, {});
  }

  onDeleteItem(item: any = null): void {
    let selectedRows: any[] = [];
    if (item === null) {
      selectedRows = this.gridApi.getSelectedRows();
    } else {
      selectedRows = [item];
    }

    if (selectedRows.length === 0) {
      this.message.warning('Vui lòng chọn ít nhất một bản ghi để xóa');
      return;
    }

    this.modalService.confirm({
      nzTitle: 'Xác nhận xóa',
      nzContent: `Bạn có chắc chắn muốn xóa ${selectedRows.length} chứng thư số đã chọn?`,
      nzOkText: 'Xóa',
      nzOkType: 'primary',
      nzOkDanger: true,
      nzOnOk: () => this.confirmDelete(selectedRows),
      nzCancelText: 'Hủy'
    });
  }

  confirmDelete(items: any[]): void {
    const deletePromises = items.map(item => this.chungThuSoApiService.delete(item.id).toPromise());

    Promise.all(deletePromises).then(
      (results: any[]) => {
        const successCount = results.filter(res => res.data).length;
        const failCount = results.length - successCount;

        if (failCount === 0) {
          this.message.success(`Xóa thành công ${successCount} chứng thư số`);
        } else {
          this.message.warning(`Xóa thành công ${successCount}, thất bại ${failCount} chứng thư số`);
        }
        this.initGridData();
      },
      (error: any) => {
        this.message.error('Có lỗi xảy ra khi xóa dữ liệu');
      }
    );
  }

  onModalEventEmmit(event: any): void {
    if (event.type === EVENT_TYPE.SUCCESS) {
      this.initGridData();
    }
    this.modal.isShow = false;
  }

  onOpenVisnamAccountManager(): void {
    this.visnamAccountModalVisible = true;
  }

  onVisnamAccountModalEventEmmit(event: any): void {
    if (event.type === EVENT_TYPE.SUCCESS) {
      this.initGridData(); // Reload grid to show new certificates if synced
    }
    this.visnamAccountModalVisible = false;
  }
  //#endregion Event
}
