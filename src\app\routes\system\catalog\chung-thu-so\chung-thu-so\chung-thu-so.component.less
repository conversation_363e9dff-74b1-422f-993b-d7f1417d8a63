/* Component specific styles for ChungThuSo */
.padding-bottom-10 {
  padding-bottom: 10px;
}

.pull-right {
  text-align: right;
}

.btn-primary {
  margin-left: 8px;
}

.btn-secondary {
  margin-left: 8px;
  background-color: #1890ff;
  border-color: #1890ff;
  color: white;
}

.btn-secondary:hover {
  background-color: #40a9ff;
  border-color: #40a9ff;
  color: white;
}

.btn-danger {
  margin-left: 8px;
}

.btn-reload {
  margin-right: 8px;
}

#chung-thu-so-grid {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
}

.ag-theme-alpine {
  --ag-header-height: 40px;
  --ag-row-height: 40px;
}

/* Custom styles for certificate status */
.certificate-expired {
  color: #ff4d4f;
  font-weight: 500;
}

.certificate-active {
  color: #52c41a;
  font-weight: 500;
}

.certificate-inactive {
  color: #d9d9d9;
  font-weight: 500;
}
