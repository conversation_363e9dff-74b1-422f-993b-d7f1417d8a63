<page-header-wrapper [title]="title" onBack="{() => window.history.back()}">
  <nz-card [nzBordered]="false">
    <nz-row>
      <nz-col nzSm="16" nzXS="24" nzMd="16" class="padding-bottom-10">
        <form [formGroup]="form">
          <nz-row [nzGutter]="16">
            <nz-col>
              <button
                nz-button
                nzType="default"
                (click)="btnReload.click($event)"
                class="btn-reload"
                *ngIf="btnReload.visible && btnReload.grandAccess"
              >
                <i nz-icon nzType="reload" nzTheme="outline"></i>{{ btnReload.title }}
              </button>
            </nz-col>

            <nz-col nzSm="8" nzXS="24" nzMd="8">
              <nz-form-item class="form-item-flex">
                <nz-form-control>
                  <nz-select
                    nzShowSearch
                    [nzAllowClear]="true"
                    id="phanHe"
                    formControlName="idPh"
                    (ngModelChange)="initGridData()"
                    class="custom-select"
                    name="phanHe"
                    nzPlaceHolder="{{ 'workflow-map-function.table.phan-he' | i18n }}"
                  >
                    <nz-option *ngFor="let p of lstPhanHe" [nzValue]="p.idPh" [nzLabel]="p.phanHe"></nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </nz-col>
            <nz-col nzSm="8" nzXS="24" nzMd="6">
              <nz-form-item class="form-item-flex">
                <nz-form-control>
                  <nz-select
                    nzShowSearch
                    [nzAllowClear]="true"
                    id="trangThai"
                    formControlName="isActive"
                    (ngModelChange)="initGridData()"
                    class="custom-select"
                    name="trangThai"
                    nzPlaceHolder="{{ 'workflow-map-function.table.is-active' | i18n }}"
                  >
                    <nz-option *ngFor="let p of lstTrangThai" [nzValue]="p.value" [nzLabel]="p.label"></nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </nz-col>
          </nz-row>
        </form>
      </nz-col>

      <nz-col nzSm="8" nzXS="24" nzMd="8" class="pull-right padding-bottom-10">
        <nz-input-group nzSearch [nzAddOnAfter]="suffixIconButton">
          <input
            type="text"
            [(ngModel)]="filter.textSearch"
            nz-input
            placeholder="{{ 'function.province.search-box.placeholder' | i18n }}"
            (keyup.enter)="initGridData()"
          />
        </nz-input-group>
        <ng-template #suffixIconButton>
          <button nz-button nzType="default" nzSearch (click)="initGridData()"><span nz-icon nzType="search"></span></button>
        </ng-template>
      </nz-col>
    </nz-row>

    <nz-row>
      <ag-grid-angular
        #agGrid
        style="width: 100%; height: 70vh"
        id="application-grid"
        class="ag-theme-alpine"
        [columnDefs]="columnDefs"
        [defaultColDef]="defaultColDef"
        [suppressRowClickSelection]="true"
        rowSelection="multiple"
        [rowData]="grid.rowData"
        (selectionChanged)="onSelectionChanged($event)"
        (cellDoubleClicked)="onCellDoubleClicked($event)"
        [overlayLoadingTemplate]="overlayLoadingTemplate"
        [overlayNoRowsTemplate]="overlayNoRowsTemplate"
        [components]="frameworkComponents"
        [excelStyles]="excelStyles"
        (gridReady)="onGridReady($event)"
      >
      </ag-grid-angular>
      <hr />
    </nz-row>
    <app-ag-grid-pagination
      [grid]="grid"
      [filter]="filter"
      [pageSizeOptions]="pageSizeOptions"
      (pageNumberChange)="onPageNumberChange()"
      (pageSizeChange)="onPageSizeChange()"
    ></app-ag-grid-pagination>
  </nz-card>
</page-header-wrapper>

<app-workflow-map-function-item
  #itemModal
  [isVisible]="modal.isShow"
  [item]="modal.item"
  [type]="modal.type"
  [option]="modal.option"
  (eventEmmit)="onModalEventEmmit($event)"
>
</app-workflow-map-function-item>
