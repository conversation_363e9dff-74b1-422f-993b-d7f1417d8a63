import { ChangeDetectorRef, Component, ElementRef, Inject, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { I18NService } from '@core';
import { ACLService } from '@delon/acl';
import { ALAIN_I18N_TOKEN } from '@delon/theme';
import { QueryFilerModel, GridModel, ButtonModel } from '@model';
import { PhanHeApiService } from '@service';
import { BtnCellRenderComponent, DateCellRenderComponent, StatusCellRenderComponent } from '@shared';
import {
  AG_GIRD_CELL_STYLE,
  EXCEL_STYLES_DEFAULT,
  LIST_STATUS,
  QUERY_FILTER_DEFAULT,
  OVERLAY_LOADING_TEMPLATE,
  OVERLAY_NOROW_TEMPLATE,
  FORM_TYPE,
  EVENT_TYPE,
  PAGE_SIZE_OPTION_DEFAULT,
  TRANG_THAI_THAM_SO_CONSTANTS
} from '@util';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzModalService } from 'ng-zorro-antd/modal';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { Subscription } from 'rxjs';
import { WorkflowMapFunctionApiService } from 'src/app/services/api/workflow-map-function.service';

import { WorkflowMapFunctionItemComponent } from '../workflow-map-function-item/workflow-map-function-item.component';

@Component({
  selector: 'app-workflow-map-function',
  templateUrl: './workflow-map-function.component.html',
  styleUrls: ['./workflow-map-function.component.less']
})
export class WorkflowMapFunctionComponent implements OnInit {
  @ViewChild(WorkflowMapFunctionItemComponent, { static: false }) itemModal!: { initData: (arg0: {}, arg1: string, option: any) => void };
  constructor(
    private workflowMapFunctionApiService: WorkflowMapFunctionApiService,
    private phanHeApiService: PhanHeApiService,
    private aclService: ACLService,
    private notification: NzNotificationService,
    private modalService: NzModalService,
    private router: Router,
    fb: FormBuilder,
    @Inject(ALAIN_I18N_TOKEN) private i18n: I18NService,
    private cdr: ChangeDetectorRef
  ) {
    this.form = fb.group({
      idPh: [null],
      isActive: [null]
    });
    this.columnDefs = [
      {
        field: 'index',
        headerName: this.i18n.fanyi('app.common.table.grid-index'),
        minWidth: 80,
        maxWidth: 80
      },
      { field: 'phanHe', headerName: this.i18n.fanyi('workflow-map-function.table.phan-he'), minWidth: 120, maxWidth: 120, flex: 1 },
      { field: 'tenChucNang', headerName: this.i18n.fanyi('workflow-map-function.table.ten-chuc-nang'), minWidth: 200, flex: 1 },
      { field: 'tenQuyTrinh', headerName: this.i18n.fanyi('workflow-map-function.table.ten-quy-trinh'), minWidth: 200, flex: 1 },
      {
        field: 'maPhanLoai',
        headerName: this.i18n.fanyi('workflow-map-function.table.ma-phan-loai'),
        minWidth: 140,
        maxWidth: 140,
        flex: 1
      },
      {
        field: 'isActive',
        headerName: this.i18n.fanyi('workflow-map-function.table.is-active'),
        cellRenderer: 'statusCellRender',
        minWidth: 160,
        maxWidth: 160,
        flex: 1
      },
      {
        headerName: this.i18n.fanyi('workflow-map-function.table.date-modify'),
        minWidth: 170,
        maxWidth: 170,
        flex: 1,
        valueGetter: (params: any) => params.data?.modifiedDate || params.data?.createdDate,
        cellRenderer: 'dateCellRender'
      },
      {
        headerName: this.i18n.fanyi('app.common.table.grid-action'),
        minWidth: 110,
        maxWidth: 110,
        pinned: 'right',
        cellRenderer: 'btnCellRender',
        cellRendererParams: {
          editClicked: (item: any) => this.onEditItem(item)
        }
      }
    ];
    this.defaultColDef = {
      // flex: 1,
      minWidth: 100,
      cellStyle: AG_GIRD_CELL_STYLE,
      resizable: true
    };
    this.frameworkComponents = {
      btnCellRender: BtnCellRenderComponent,
      dateCellRender: DateCellRenderComponent,
      statusCellRender: StatusCellRenderComponent
    };
    this.excelStyles = [...EXCEL_STYLES_DEFAULT];
    //#endregion ag-grid

    //#region Init button
    this.btnReload = {
      title: this.i18n.fanyi('app.common.button.refresh'),
      visible: true,
      enable: true,
      grandAccess: true,
      click: ($event: any) => {
        this.onResetSearch(true);
      }
    };
    //#endregion Init button
  }

  isRenderComplete = false;

  listStatus = LIST_STATUS;
  filter: QueryFilerModel = { ...QUERY_FILTER_DEFAULT };
  pageSizeOptions: any[] = [];

  columnDefs: any[] = [];
  grid: GridModel = {
    dataCount: 0,
    rowData: [],
    totalData: 0
  };
  private gridApi: any;
  private gridColumnApi: any;
  defaultColDef: any;
  rowSelection = 'multiple';
  overlayLoadingTemplate = OVERLAY_LOADING_TEMPLATE;
  overlayNoRowsTemplate = OVERLAY_NOROW_TEMPLATE;
  quickText = '';
  excelStyles: any;
  frameworkComponents: any;

  btnReload: ButtonModel;

  isLoading = false;

  lstPhanHe: any;
  lstTrangThai: any = TRANG_THAI_THAM_SO_CONSTANTS;

  title = this.i18n.fanyi('function.workflow-map-function.page.title');

  modal: any = {
    type: '',
    item: {},
    isShow: false,
    option: {}
  };

  form: FormGroup;

  ngOnInit(): void {
    this.getComboboxPhanHe();
  }

  initRightOfUser(): void {}

  //#region Ag-grid
  onPageSizeChange(): void {
    this.initGridData();
  }

  onPageNumberChange(): void {
    this.initGridData();
  }

  onGridReady(params: any): void {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.initGridData();
  }

  onSelectionChanged($event: any): void {}

  onCellDoubleClicked($event: any): void {
    this.onViewItem($event.data);
  }
  //#endregion Ag-grid

  //#region Event
  onResetSearch(reloadData: boolean): void {
    this.filter.pageNumber = 1;
    this.filter.textSearch = undefined;
    this.filter['status'] = undefined;
    if (reloadData) {
      this.initGridData();
    }
  }

  onEditItem(item: any = null): void {
    if (item === null) {
      const selectedRows = this.gridApi.getSelectedRows();
      item = selectedRows[0];
    }
    this.modal = {
      type: FORM_TYPE.EDIT,
      item,
      isShow: true
    };
    this.itemModal.initData(item, FORM_TYPE.EDIT, {});
  }

  onViewItem(item: any = null): void {}

  //#endregion Event

  //#region Modal

  onModalEventEmmit(event: any): void {
    this.modal.isShow = false;
    if (event.type === EVENT_TYPE.SUCCESS) {
      this.initGridData();
    }
  }
  //#endregion Modal

  //#region API Event

  initGridData(): Subscription | undefined {
    this.isLoading = true;
    this.filter['isActive'] = this.form.value.isActive;
    this.filter['idPh'] = this.form.value.idPh;
    this.filter['propertyName'] = '';
    this.gridApi.showLoadingOverlay();
    const rs = this.workflowMapFunctionApiService.getFilter(this.filter).subscribe({
      next: (res: any) => {
        this.isLoading = false;
        if (res.data === null || res.data === undefined) {
          this.notification.error(this.i18n.fanyi('app.common.error.title'), `${res.message}`);
          return;
        }

        const dataResult = res.data;

        let i = (this.filter.pageSize ?? 0) * ((this.filter.pageNumber ?? 0) - 1);

        for (const item of dataResult.data) {
          item.index = ++i;
          item.editGrantAccess = this.aclService.canAbility('WORKFLOW_MAP_FUNCTION_EDIT');
        }
        this.grid.totalData = dataResult.totalCount;
        this.grid.dataCount = dataResult.dataCount;
        this.grid.rowData = dataResult.data;
        this.pageSizeOptions = [...PAGE_SIZE_OPTION_DEFAULT];
        this.pageSizeOptions = this.pageSizeOptions.filter(number => {
          return number < dataResult.totalCount;
        });
        this.pageSizeOptions.push(dataResult.totalCount);
      },
      error: (err: any) => {
        this.gridApi.hideOverlay();
        this.notification.error(this.i18n.fanyi('app.common.error.title'), `${err.error.message}`);
        this.isLoading = false;
      },
      complete: () => {
        this.gridApi.hideOverlay();
        this.isLoading = false;
      }
    });
    return rs;
  }
  //#endregion API Event

  //#region Combobox
  getComboboxPhanHe() {
    this.phanHeApiService.getListCombobox().subscribe({
      next: (res: any) => {
        if (res.data === null || res.data === undefined) {
          this.notification.error(this.i18n.fanyi('app.common.error.title'), `${res.message}`);
          return;
        }
        this.lstPhanHe = res?.data;
        this.cdr.detectChanges();
      },
      error: (err: any) => {
        this.notification.error(this.i18n.fanyi('app.common.error.title'), `${err.error.message}`);
      }
    });
  }

  //#endregion
}
