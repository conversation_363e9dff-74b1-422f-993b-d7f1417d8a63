# Tài liệu Cấu trúc Dự án Uni System UI

## 1. Tổng quan dự án

### Mô tả
Uni System UI là một ứng dụng Angular frontend được xây dựng dựa trên framework **NG-ALAIN** và thư viện UI **NG-ZORRO-ANTD**. <PERSON><PERSON><PERSON> là hệ thống quản trị dành cho các tổ chức giáo dục với nhiều tính năng quản lý người dùng, ph<PERSON> quyền, và tích hợp đa dạng các phương thức xác thực.

### Công nghệ sử dụng
- **Angular 15**: Framework chính
- **NG-ALAIN**: Business theme và admin panel framework
- **NG-ZORRO-ANTD**: Thư viện UI components
- **TypeScript**: Ngôn ngữ lập trình
- **LESS/SCSS**: Preprocessor CSS
- **RxJS**: Reactive programming
- **AG-Grid**: Data grid component
- **OIDC-Client**: OpenID Connect authentication

## 2. <PERSON><PERSON><PERSON> trúc thư mục

```
uni-system-ui/
├── src/
│   ├── app/                    # Mã nguồn chính của ứng dụng
│   │   ├── layout/            # Layout components (Pro layout)
│   │   ├── routes/            # Feature modules và routing
│   │   ├── shared/            # Shared components và modules
│   │   ├── shared-ui/         # Shared UI components (submodule)
│   │   ├── services/          # Application services
│   │   ├── models/            # Data models và interfaces
│   │   ├── utils/             # Utility functions và constants
│   │   ├── app.module.ts      # Root module
│   │   └── global-config.module.ts # Global configuration
│   ├── assets/                # Static assets
│   ├── environments/          # Environment configurations
│   └── styles/               # Global styles
├── _mock/                     # Mock data cho development
├── _cli-tpl/                  # CLI templates
├── project-documents/         # Tài liệu dự án
├── package.json              # Dependencies và scripts
├── angular.json              # Angular CLI configuration
└── tsconfig.json             # TypeScript configuration
```

### Chi tiết các thư mục chính

#### 2.1. `src/app/layout/`
Chứa các layout components:
- **pro/**: Pro layout với sidebar, header, footer
- **passport/**: Layout cho trang đăng nhập

#### 2.2. `src/app/routes/`
Chứa các feature modules:
- **dashboard/**: Trang chủ
- **system/**: Quản lý hệ thống (user, role, log)
- **resource/**: Quản lý tài nguyên
- **passport/**: Authentication pages
- **exception/**: Error pages

#### 2.3. `src/app/shared/`
Shared components và modules:
- **components/**: Reusable components
- **ag-grid/**: AG-Grid customizations
- **json-schema/**: JSON Schema form widgets
- **shared.module.ts**: Main shared module

#### 2.4. `src/app/shared-ui/` (Submodule)
Shared UI components từ submodule:
- **core/**: Core services (startup, i18n, guards)
- **services/**: Shared services (auth, API)
- **components/**: Reusable UI components
- **utils/**: Utility functions

## 3. Kiến trúc ứng dụng

### 3.1. Module Structure
```
AppModule (Root)
├── GlobalConfigModule (Global config)
├── CoreModule (Core services)
├── SharedModule (Shared components)
├── LayoutModule (Layout components)
└── RoutesModule (Feature routes)
    ├── DashboardModule
    ├── SystemModule
    ├── ResourceModule
    └── PassportModule
```

### 3.2. Service Architecture
- **StartupService**: Khởi tạo ứng dụng, load config
- **AuthService**: Xử lý authentication
- **I18NService**: Đa ngôn ngữ
- **API Services**: Giao tiếp với backend

### 3.3. Routing Strategy
- **Lazy Loading**: Feature modules được load theo yêu cầu
- **Route Guards**: AuthGuard, StartPageGuard
- **Route Reuse**: Sử dụng ReuseTabStrategy

## 4. Hệ thống xác thực

### 4.1. Các phương thức authType
Hệ thống hỗ trợ 4 phương thức xác thực:

#### **SSO (Single Sign-On)**
- Sử dụng Identity Server với OIDC protocol
- Library: `oidc-client`
- Flow: Authorization Code với PKCE

```typescript
// Cấu hình SSO
identiyServer: {
  baseUrl: 'https://moodle.unisoft.edu.vn',
  clientId: 'uni-hrm-portal-client',
  scopes: 'email openid profile offline_access'
}
```

#### **KeyCloak OAuth2**
- Tương tự SSO nhưng sử dụng KeyCloak server
- Cũng sử dụng `oidc-client` library
- Validation: Kiểm tra `user.sub` property không null

```typescript
// Cấu hình KeyCloak
keycloakServer: {
  baseUrl: 'http://localhost:8080',
  realm: 'unisoft',
  clientId: 'unisoft-client',
  scopes: 'openid profile email'
}
```

#### **HOU (Custom CAS)**
- Sử dụng CAS (Central Authentication Service)
- Custom implementation cho HOU education system

```typescript
// Cấu hình HOU
houCasServer: {
  serverUrl: 'https://cas.hou.edu.vn'
}
```

#### **JWT (Traditional Login)**
- Form-based authentication với username/password
- JWT token management
- Redirect đến `/passport/login`

### 4.2. Authentication Flow

1. **Startup**: StartupService load environment config
2. **Route Guard**: AuthGuard kiểm tra authentication status
3. **Login**: Redirect đến phương thức auth tương ứng
4. **Callback**: OIDC callback xử lý token
5. **Token Storage**: Lưu token và user info
6. **Permission**: Load permissions và setup ACL

## 5. Quy trình phát triển

### 5.1. Setup môi trường

```bash
# Clone repository
git clone <repository-url>
cd uni-system-ui

# Install dependencies
yarn install

# Update submodules
git submodule update --init --recursive

# Start development server
npm start
```

### 5.2. Development Scripts

```bash
# Development
npm start                    # Port 8102
npm run start-local         # Local environment
npm run start-test          # Test environment

# Build
npm run build               # Production build
npm run build-origin        # Build without env update

# Testing
npm run test                # Unit tests
npm run test-coverage       # Coverage report
npm run e2e                 # E2E tests

# Linting
npm run lint                # TypeScript + Style lint
```

### 5.3. Environment Configuration

Các file environment:
- `environment.ts`: Development
- `environment.local.ts`: Local development
- `environment.test.ts`: Test environment
- `environment.prod.ts`: Production

Dynamic config loading từ `assets/env.json`:
```json
{
  "authType": "sso",
  "api": {
    "baseUrl": "https://api.example.com"
  },
  "identiyServer": {
    "baseUrl": "https://identity.example.com",
    "clientId": "client-id"
  }
}
```

## 6. Các thư viện quan trọng

### 6.1. Core Dependencies
- **@angular/core**: Angular framework
- **@delon/**: NG-ALAIN ecosystem
  - `@delon/theme`: Theme system
  - `@delon/auth`: Authentication
  - `@delon/acl`: Access Control List
  - `@delon/abc`: Business components
- **ng-zorro-antd**: Ant Design components
- **rxjs**: Reactive programming

### 6.2. Authentication & Security
- **@auth0/angular-jwt**: JWT handling
- **oidc-client**: OpenID Connect client

### 6.3. Data & Forms
- **ag-grid-angular**: Data grid
- **@delon/form**: Dynamic forms
- **@angular/forms**: Reactive forms

### 6.4. UI & UX
- **@ckeditor/**: Rich text editor
- **ng-gallery**: Image gallery
- **perfect-scrollbar**: Custom scrollbar
- **screenfull**: Fullscreen API

### 6.5. Utilities
- **dayjs**: Date manipulation
- **file-saver**: File download
- **js-base64**: Base64 encoding
- **sortablejs**: Drag & drop sorting

## 7. Hướng dẫn cho developer mới

### 7.1. Bước đầu tiên
1. **Đọc tài liệu**: Familiarize với NG-ALAIN và NG-ZORRO
2. **Setup environment**: Cài đặt Node.js, Yarn, Angular CLI
3. **Clone và setup**: Follow setup instructions
4. **Hiểu cấu trúc**: Explore codebase structure

### 7.2. Tạo feature mới

#### Tạo module mới:
```bash
ng g module routes/new-feature --routing
ng g component routes/new-feature/new-feature
```

#### Thêm vào routing:
```typescript
// routes-routing.module.ts
{
  path: 'new-feature',
  loadChildren: () => import('./new-feature/new-feature.module').then(m => m.NewFeatureModule),
  canActivate: [AuthGuard]
}
```

#### Thêm menu:
```typescript
// utils/constants.ts - MENU_CONSTANTS
{
  text: 'New Feature',
  i18n: 'menu.new-feature',
  icon: 'anticon anticon-star',
  link: '/new-feature',
  acl: ['PERMISSION_NAME']
}
```

### 7.3. Best Practices

#### Code Style:
- Sử dụng TypeScript strict mode
- Follow Angular style guide
- Sử dụng ESLint và Prettier
- Reactive programming với RxJS

#### Component Development:
- Sử dụng OnPush change detection
- Implement OnDestroy cho cleanup
- Sử dụng NG-ZORRO components
- Responsive design

#### Service Development:
- Injectable với providedIn: 'root'
- Error handling với catchError
- Loading states management
- Type safety với interfaces

### 7.4. Testing Guidelines

#### Unit Testing:
```bash
ng test --watch=false --code-coverage
```

#### E2E Testing:
```bash
ng e2e
```

#### Manual Testing:
- Test trên multiple browsers
- Test responsive design
- Test authentication flows
- Test permissions

### 7.5. Deployment

#### Build for production:
```bash
npm run build
```

#### Docker deployment:
```bash
# Build image
docker build -t uni-system-ui .

# Run container
docker run -d -p 8102:80 uni-system-ui
```

#### Environment setup:
- Cấu hình `env.json` cho từng environment
- Setup reverse proxy (nginx/IIS)
- SSL certificate configuration

---

*Tài liệu này sẽ được cập nhật thường xuyên theo sự phát triển của dự án.*
