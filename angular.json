{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"ng-alain": {"projectType": "application", "root": "", "sourceRoot": "src", "prefix": "app", "schematics": {"@schematics/angular:component": {"style": "less"}, "@schematics/angular:application": {"strict": true}}, "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist", "index": "src/index.html", "main": "src/main.ts", "tsConfig": "tsconfig.app.json", "polyfills": ["zone.js"], "assets": ["src/assets", "src/favicon.ico", {"glob": "**/*", "input": "./node_modules/@ant-design/icons-angular/src/inline-svg/", "output": "/assets/"}], "styles": ["node_modules/perfect-scrollbar/css/perfect-scrollbar.css", "node_modules/quill/dist/quill.snow.css", "src/styles.less", "src/styles.scss"], "scripts": ["node_modules/quill/dist/quill.min.js", "node_modules/perfect-scrollbar/dist/perfect-scrollbar.js"], "allowedCommonJsDependencies": ["ajv", "ajv-formats", "mockjs", "file-saver", "extend", "clone-deep", "@fullcalendar/core/locales/zh-cn", "moment", "oidc-client", "@ckeditor/ckeditor5-build-classic", "dayjs", "@optimajet/workflow-designer/dist/workflowdesignerfull.min.js", "j<PERSON>y"], "stylePreprocessorOptions": {"includePaths": ["node_modules/"]}}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all", "budgets": [{"type": "initial", "maximumWarning": "20mb", "maximumError": "500mb"}]}, "local": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.local.ts"}], "buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}, "test": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.test.ts"}], "buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "ng-alain:build", "proxyConfig": "proxy.conf.js"}, "configurations": {"production": {"browserTarget": "ng-alain:build:production"}, "local": {"browserTarget": "ng-alain:build:local"}, "test": {"browserTarget": "ng-alain:build:test"}, "development": {"browserTarget": "ng-alain:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "ng-alain:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "scripts": ["node_modules/quill/dist/quill.min.js", "node_modules/perfect-scrollbar/dist/perfect-scrollbar.js"], "styles": [], "assets": ["src/assets"]}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}, "e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "ng-alain:serve"}, "configurations": {"production": {"devServerTarget": "ng-alain:serve:production"}}}}}}, "cli": {"packageManager": "yarn", "schematicCollections": ["@schematics/angular", "ng-alain"], "analytics": "87c0638b-1ba9-46ef-ad09-4ad0b43dd911"}, "schematics": {"@angular-eslint/schematics:application": {"setParserOptionsProject": true}, "@angular-eslint/schematics:library": {"setParserOptionsProject": true}}}