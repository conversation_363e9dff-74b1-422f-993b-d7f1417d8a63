<page-header-wrapper [title]="title" onBack="{() => window.history.back()}">
  <nz-card [nzBordered]="false">
    <nz-row>
      <nz-col nzSm="16" nzXs="24" nzMd="16" class="padding-bottom-10">
        <button
          nz-button
          nzType="default"
          (click)="btnReload.click($event)"
          class="btn-reload"
          *ngIf="btnReload.visible && btnReload.grandAccess"
        >
          <i nz-icon nzType="reload" nzTheme="outline"></i>{{ btnReload.title }}
        </button>
        <!-- Nút thêm mới đã bị ẩn, chỉ cho phép đồng bộ qua Visnam -->
        <!-- <button nz-button nzType="primary" (click)="btnAdd.click($event)" class="btn-primary" *ngIf="btnAdd.visible && btnAdd.grandAccess">
          <i nz-icon nzType="file-add" nzTheme="fill"></i>{{ btnAdd.title }}
        </button> -->
        <button nz-button nzType="default" (click)="onOpenVisnamAccountManager()" class="btn-secondary">
          <i nz-icon nzType="api" nzTheme="outline"></i>Quản lý tài khoản Visnam
        </button>
        <nz-alert
          nzType="info"
          nzMessage="Hướng dẫn"
          nzDescription="Để thêm chứng thư số, vui lòng sử dụng chức năng 'Quản lý tài khoản Visnam' để đồng bộ từ hệ thống Visnam."
          nzShowIcon
          nzCloseable
          style="margin-left: 8px; display: inline-block; max-width: 400px"
        ></nz-alert>
      </nz-col>
      <nz-col nzSm="8" nzXs="24" nzMd="8" class="pull-right padding-bottom-10">
        <nz-input-group nzSearch [nzAddOnAfter]="suffixIconButton">
          <input
            type="text"
            [(ngModel)]="filter.textSearch"
            nz-input
            placeholder="Tìm kiếm theo số serial, tên chủ thể, nhà phát hành..."
            (keyup.enter)="initGridData()"
          />
        </nz-input-group>
        <ng-template #suffixIconButton>
          <button nz-button nzType="default" nzSearch (click)="initGridData()"><span nz-icon nzType="search"></span></button>
        </ng-template>
      </nz-col>
    </nz-row>
    <nz-row>
      <ag-grid-angular
        #agGrid
        style="width: 100%; height: 70vh"
        id="chung-thu-so-grid"
        class="ag-theme-alpine"
        [columnDefs]="columnDefs"
        [defaultColDef]="defaultColDef"
        [suppressRowClickSelection]="true"
        rowSelection="multiple"
        [rowData]="grid.rowData"
        (selectionChanged)="onSelectionChanged($event)"
        (cellDoubleClicked)="onCellDoubleClicked($event)"
        [overlayLoadingTemplate]="overlayLoadingTemplate"
        [overlayNoRowsTemplate]="overlayNoRowsTemplate"
        [components]="frameworkComponents"
        [excelStyles]="excelStyles"
        (gridReady)="onGridReady($event)"
        [loading]="isLoading"
      >
      </ag-grid-angular>
      <hr />
    </nz-row>
    <app-ag-grid-pagination
      [grid]="grid"
      [filter]="filter"
      [pageSizeOptions]="pageSizeOptions"
      (pageNumberChange)="onPageNumberChange()"
      (pageSizeChange)="onPageSizeChange()"
    ></app-ag-grid-pagination>
  </nz-card>
</page-header-wrapper>

<app-chung-thu-so-item
  #itemModal
  [isVisible]="modal.isShow"
  [item]="modal.item"
  [type]="modal.type"
  [option]="modal.option"
  (eventEmmit)="onModalEventEmmit($event)"
>
</app-chung-thu-so-item>

<app-visnam-tai-khoan-ket-noi
  #visnamAccountModal
  [isVisible]="visnamAccountModalVisible"
  (eventEmmit)="onVisnamAccountModalEventEmmit($event)"
>
</app-visnam-tai-khoan-ket-noi>
