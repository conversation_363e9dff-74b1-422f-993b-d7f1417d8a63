import { Component, EventEmitter, Inject, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { I18NService } from '@core';
import { ACLService } from '@delon/acl';
import { ALAIN_I18N_TOKEN } from '@delon/theme';

import { ButtonModel } from '@model';
import { cleanForm, EVENT_TYPE, FORM_TYPE } from '@util';
import dayjs from 'dayjs';
import { NzMessageService } from 'ng-zorro-antd/message';
import { Subscription } from 'rxjs';
import { ApiKeyService } from 'src/app/services/api/api-key.service';
import { UserApiService } from 'src/app/services/api/user-api.service';

@Component({
  selector: 'app-api-key-item',
  templateUrl: './api-key-item.component.html',
  styleUrls: ['./api-key-item.component.less']
})
export class ApiKeyItemComponent implements OnInit {
  @Input() type = 'add';
  @Input() item: any;
  @Input() isVisible = false;
  @Input() option: any;
  @Output() readonly eventEmmit = new EventEmitter<any>();

  form: FormGroup;

  isInfo = false;
  isEdit = false;
  isAdd = false;
  tittle = '';

  isLoading = false;

  btnSave: ButtonModel;
  btnCancel: ButtonModel;
  btnEdit: ButtonModel;

  userList: any[] = [];

  constructor(
    private fb: FormBuilder,
    private messageService: NzMessageService,
    private apiKeyService: ApiKeyService,
    private userApiService: UserApiService,
    @Inject(ALAIN_I18N_TOKEN) private i18n: I18NService,
    private aclService: ACLService
  ) {
    this.btnSave = {
      title: this.i18n.fanyi('app.common.button.save'),
      visible: true,
      enable: true,
      grandAccess: true,
      click: () => {
        this.save();
      }
    };
    this.btnCancel = {
      title: this.i18n.fanyi('app.common.button.close'),
      visible: true,
      enable: true,
      grandAccess: true,
      click: () => {
        this.handleCancel();
      }
    };
    this.btnEdit = {
      title: this.i18n.fanyi('app.common.button.edit'),
      visible: true,
      enable: true,
      grandAccess: true,
      click: () => {
        this.updateFormToEdit();
      }
    };
    this.form = this.fb.group({
      userId: [null, [Validators.required]],
      key: [null],
      description: [null],
      validFrom: [null, [Validators.required]],
      validTo: [null, [Validators.required]],
      isActive: [true]
    });
  }

  handleCancel(): void {
    this.isVisible = false;
    this.eventEmmit.emit({ type: EVENT_TYPE.CLOSE });
  }

  ngOnInit(): void {
    this.initRightOfUser();
    this.loadUserList();
  }

  initRightOfUser(): void {
    this.btnSave.grandAccess = this.aclService.canAbility('API_KEY_ADD') || this.aclService.canAbility('API_KEY_EDIT');
    this.btnEdit.grandAccess = this.aclService.canAbility('API_KEY_EDIT');
  }

  loadUserList(): void {
    this.userApiService.getListCombobox().subscribe({
      next: (res: any) => {
        if (res.data) {
          this.userList = res.data;
        }
      },
      error: (err: any) => {
        console.error('Error loading user list:', err);
      }
    });
  }

  //#region Update-form-type
  updateFormToAdd(): void {
    this.isInfo = false;
    this.isEdit = false;
    this.isAdd = true;
    this.tittle = this.i18n.fanyi('function.api-key.modal.title-add');
    this.item = {};
    this.form.get('userId')?.enable();
    this.form.get('key')?.enable();
    this.form.get('description')?.enable();
    this.form.get('validFrom')?.enable();
    this.form.get('validTo')?.enable();
    this.form.get('isActive')?.enable();
  }

  updateFormToInfo(): void {
    this.isInfo = true;
    this.isEdit = false;
    this.isAdd = false;
    this.tittle = this.i18n.fanyi('function.api-key.modal.title-info');
    this.form.get('userId')?.disable();
    this.form.get('key')?.disable();
    this.form.get('description')?.disable();
    this.form.get('validFrom')?.disable();
    this.form.get('validTo')?.disable();
    this.form.get('isActive')?.disable();
  }

  updateFormToEdit(): void {
    this.isInfo = false;
    this.isEdit = true;
    this.isAdd = false;
    this.tittle = this.i18n.fanyi('function.api-key.modal.title-edit');
    this.form.get('userId')?.enable();
    this.form.get('key')?.disable(); // Key không được phép sửa
    this.form.get('description')?.enable();
    this.form.get('validFrom')?.enable();
    this.form.get('validTo')?.enable();
    this.form.get('isActive')?.enable();
  }

  resetForm(): void {
    this.form.reset();
    this.form.get('isActive')?.setValue(true);
    // Set default dates
    const now = new Date();
    const nextYear = new Date();
    nextYear.setFullYear(now.getFullYear() + 1);
    this.form.get('validFrom')?.setValue(now);
    this.form.get('validTo')?.setValue(nextYear);
  }

  updateDataToForm(data: any): void {
    this.form.get('userId')?.setValue(data.userId);
    this.form.get('key')?.setValue(data.key);
    this.form.get('description')?.setValue(data.description);
    this.form.get('validFrom')?.setValue(data.validFrom ? new Date(data.validFrom) : null);
    this.form.get('validTo')?.setValue(data.validTo ? new Date(data.validTo) : null);
    this.form.get('isActive')?.setValue(data.isActive);
  }

  //#endregion Update-form-type

  public initData(data: any, type: any = null, option: any = {}): void {
    this.resetForm();
    this.isLoading = false;
    this.item = data;
    this.type = type;
    this.option = option;

    if (this.item?.id) {
      this.getDataInfo(this.item.id);
    }
    switch (type) {
      case FORM_TYPE.ADD:
        this.updateFormToAdd();
        break;
      case FORM_TYPE.INFO:
        this.updateFormToInfo();
        break;
      case FORM_TYPE.EDIT:
        this.updateFormToEdit();
        break;
      default:
        this.updateFormToAdd();
        break;
    }
  }

  closeModalReloadData(): void {
    this.isVisible = false;
    this.eventEmmit.emit({ type: EVENT_TYPE.SUCCESS });
  }

  getDataInfo(id: any): Subscription | undefined {
    this.isLoading = true;
    const rs = this.apiKeyService.getById(id).subscribe({
      next: (res: any) => {
        this.isLoading = false;
        if (res.data === null || res.data === undefined) {
          this.messageService.error(`${res.message}`);
          return;
        }
        this.item = res.data;
        this.updateDataToForm(res.data);
      },
      error: () => {
        this.isLoading = false;
      },
      complete: () => {
        this.isLoading = false;
      }
    });
    return rs;
  }

  save(): Subscription | undefined {
    this.isLoading = true;

    cleanForm(this.form);
    for (const i in this.form.controls) {
      this.form.controls[i].markAsDirty();
      this.form.controls[i].updateValueAndValidity();
    }
    if (!this.form.valid) {
      this.isLoading = false;
      this.messageService.error(this.i18n.fanyi('app.common.form.dirty'));
      return;
    }

    const data = {
      id: this.item.id,
      userId: this.form.get('userId')?.value,
      key: this.form.get('key')?.value,
      description: this.form.get('description')?.value,
      validFrom: dayjs(this.form.get('validFrom')?.value).format('YYYY-MM-DD'),
      validTo: dayjs(this.form.get('validTo')?.value).format('YYYY-MM-DD'),
      isActive: this.form.get('isActive')?.value
    };

    if (this.isAdd) {
      const promise = this.apiKeyService.create(data).subscribe({
        next: (res: any) => {
          this.isLoading = false;
          if (res.data === null || res.data === undefined) {
            this.messageService.error(`${res.message}`);
            return;
          }

          this.messageService.success(`${res.message}`);
          this.closeModalReloadData();
        },
        error: (err: any) => {
          this.isLoading = false;
          if (err.error) {
            this.messageService.error(`${err.error.message}`);
          } else {
            this.messageService.error(`${err.status}`);
          }
        },
        complete: () => (this.isLoading = false)
      });
      return promise;
    } else if (this.isEdit) {
      const promise = this.apiKeyService.update(this.item.id, data).subscribe({
        next: (res: any) => {
          this.isLoading = false;
          if (res.data === null || res.data === undefined) {
            this.messageService.error(`${res.message}`);
            return;
          }
          this.messageService.success(`${res.message}`);
          this.closeModalReloadData();
        },
        error: (err: any) => {
          this.isLoading = false;
          if (err.error) {
            this.messageService.error(`${err.error.message}`);
          } else {
            this.messageService.error(`${err.status}`);
          }
        },
        complete: () => (this.isLoading = false)
      });
      return promise;
    } else {
      return;
    }
  }
}
