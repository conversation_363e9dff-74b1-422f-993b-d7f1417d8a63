import { HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { _HttpClient } from '@delon/theme';
import { environment } from '@env/environment';
// RxJS
import { Observable } from 'rxjs';

import { workflowRouter } from '../utils/shared-api-router';

@Injectable({
  providedIn: 'root'
})
export class WorkflowApiService {
  constructor(private http: _HttpClient) {}

  getHistoryWorkflow(processId: string): Observable<any> {
    return this.http.get(environment.api.baseUrl + workflowRouter.getHistoryWorkflow + processId);
  }

  getCombobox(): Observable<any> {
    return this.http.get(environment.api.baseUrl + workflowRouter.getComboboxWorkflow);
  }
}
