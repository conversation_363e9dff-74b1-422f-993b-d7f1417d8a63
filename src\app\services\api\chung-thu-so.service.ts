import { Injectable } from '@angular/core';
import { _HttpClient } from '@delon/theme';
import { environment } from '@env/environment';
import { QueryFilerModel } from '@model';
import { chungThuSoRouter } from '@util';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ChungThuSoService {
  constructor(private http: _HttpClient) {}

  getFilter(model: QueryFilerModel): Observable<any> {
    return this.http.post(environment.api.baseUrl + chungThuSoRouter.getFilter, model);
  }

  delete(id: any): Observable<any> {
    return this.http.delete(environment.api.baseUrl + chungThuSoRouter.delete + id);
  }

  create(model: any): Observable<any> {
    return this.http.post(environment.api.baseUrl + chungThuSoRouter.create, model);
  }

  update(id: any, model: any): Observable<any> {
    return this.http.put(environment.api.baseUrl + chungThuSoRouter.update + id, model);
  }

  getById(id: string): Observable<any> {
    return this.http.get(environment.api.baseUrl + chungThuSoRouter.getById + id);
  }

  getCombobox(): Observable<any> {
    return this.http.get(environment.api.baseUrl + chungThuSoRouter.getCombobox);
  }

  getForUser(count?: number): Observable<any> {
    const url = count 
      ? `${environment.api.baseUrl}${chungThuSoRouter.getForUser}?count=${count}`
      : environment.api.baseUrl + chungThuSoRouter.getForUser;
    return this.http.get(url);
  }

  getStatistics(): Observable<any> {
    return this.http.get(environment.api.baseUrl + chungThuSoRouter.getStatistics);
  }

  createBulk(model: any): Observable<any> {
    return this.http.post(environment.api.baseUrl + chungThuSoRouter.createBulk, model);
  }
}
