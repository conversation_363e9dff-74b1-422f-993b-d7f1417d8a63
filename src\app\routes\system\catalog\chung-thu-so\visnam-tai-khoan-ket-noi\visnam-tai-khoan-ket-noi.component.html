<nz-modal
  [(nzVisible)]="isVisible"
  [nzTitle]="modalTitle"
  [nzContent]="modalContent"
  [nzFooter]="modalFooter"
  nzMaskClosable="false"
  nzWidth="1200px"
  (nzOnCancel)="handleClose()"
>
  <ng-template #modalTitle>
    <i nz-icon nzType="api" nzTheme="outline"></i>
    {{ title }}
  </ng-template>

  <ng-template #modalContent>
    <div *ngIf="!isShowForm">
      <!-- Grid hiển thị danh sách tài khoản kết nối -->
      <nz-row class="margin-bottom-16">
        <nz-col nzSm="16" nzXs="24" nzMd="16" class="padding-bottom-10">
          <button
            nz-button
            nzType="primary"
            (click)="btnAdd.click($event)"
            class="btn-primary"
            *ngIf="btnAdd.visible && btnAdd.grandAccess"
          >
            <i nz-icon nzType="plus" nzTheme="outline"></i>{{ btnAdd.title }}
          </button>
        </nz-col>
        <nz-col nzSm="8" nzXs="24" nzMd="8" class="pull-right padding-bottom-10">
          <nz-input-group nzSearch [nzAddOnAfter]="suffixIconButton">
            <input type="text" [(ngModel)]="filter.textSearch" nz-input placeholder="Tìm kiếm theo key..." (keyup.enter)="initGridData()" />
          </nz-input-group>
          <ng-template #suffixIconButton>
            <button nz-button nzType="default" nzSearch (click)="initGridData()"><span nz-icon nzType="search"></span></button>
          </ng-template>
        </nz-col>
      </nz-row>

      <nz-row>
        <ag-grid-angular
          #agGrid
          style="width: 100%; height: 50vh"
          id="visnam-tai-khoan-ket-noi-grid"
          class="ag-theme-alpine"
          [columnDefs]="columnDefs"
          [defaultColDef]="defaultColDef"
          [suppressRowClickSelection]="true"
          rowSelection="multiple"
          [rowData]="grid.rowData"
          (selectionChanged)="onSelectionChanged($event)"
          (cellDoubleClicked)="onCellDoubleClicked($event)"
          [overlayLoadingTemplate]="overlayLoadingTemplate"
          [overlayNoRowsTemplate]="overlayNoRowsTemplate"
          [components]="frameworkComponents"
          [excelStyles]="excelStyles"
          (gridReady)="onGridReady($event)"
          [loading]="isLoading"
        >
        </ag-grid-angular>
      </nz-row>

      <nz-row class="margin-top-16">
        <app-ag-grid-pagination
          [grid]="grid"
          [filter]="filter"
          [pageSizeOptions]="pageSizeOptions"
          (pageNumberChange)="onPageNumberChange()"
          (pageSizeChange)="onPageSizeChange()"
        ></app-ag-grid-pagination>
      </nz-row>
    </div>

    <!-- Form thêm/sửa tài khoản kết nối -->
    <div *ngIf="isShowForm">
      <form nz-form [formGroup]="form" (ngSubmit)="save()">
        <nz-form-item>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="key">Key</nz-form-label>
          <nz-form-control [nzSm]="17" [nzXs]="24" nzErrorTip="Vui lòng nhập key">
            <input nz-input formControlName="key" id="key" placeholder="Nhập key kết nối Visnam" maxlength="500" />
          </nz-form-control>
        </nz-form-item>

        <nz-form-item>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="secret">Secret</nz-form-label>
          <nz-form-control [nzSm]="17" [nzXs]="24" nzErrorTip="Vui lòng nhập secret">
            <input nz-input type="password" formControlName="secret" id="secret" placeholder="Nhập secret kết nối Visnam" maxlength="500" />
          </nz-form-control>
        </nz-form-item>
      </form>
    </div>
  </ng-template>

  <ng-template #modalFooter>
    <!-- Footer cho form -->
    <div *ngIf="isShowForm">
      <button
        nz-button
        nzType="primary"
        class="btn-primary"
        *ngIf="btnSave.visible && btnSave.grandAccess"
        [nzLoading]="isLoading"
        (click)="btnSave.click($event)"
      >
        <i nz-icon nzType="save" nzTheme="fill"></i>{{ btnSave.title }}
      </button>
      <button
        nz-button
        nzType="default"
        class="btn-secondary"
        *ngIf="btnCancel.visible && btnCancel.grandAccess"
        (click)="btnCancel.click($event)"
      >
        <i nz-icon nzType="rollback" nzTheme="outline"></i>{{ btnCancel.title }}
      </button>
    </div>

    <!-- Footer cho grid -->
    <div *ngIf="!isShowForm">
      <button
        nz-button
        nzType="default"
        class="btn-warning"
        *ngIf="btnClose.visible && btnClose.grandAccess"
        (click)="btnClose.click($event)"
      >
        <i nz-icon nzType="close-circle" nzTheme="fill"></i>{{ btnClose.title }}
      </button>
    </div>
  </ng-template>
</nz-modal>
