<nz-modal
  [(nzVisible)]="isVisible"
  [nzTitle]="modalTitle"
  [nzContent]="modalContent"
  [nzFooter]="modalFooter"
  nzMaskClosable="false"
  nzWidth="1000px"
  (nzOnCancel)="handleCancel()"
>
  <ng-template #modalTitle> {{ tittle }} </ng-template>

  <ng-template #modalContent>
    <!-- Hiển thị thông tin chứng thư số (chỉ đọc) -->
    <nz-descriptions nzBordered [nzColumn]="1" nzSize="small">
      <nz-descriptions-item nzTitle="Số serial">{{ item.serialNumber || 'N/A' }}</nz-descriptions-item>
      <nz-descriptions-item nzTitle="Tên chủ thể">{{ item.subjectName || 'N/A' }}</nz-descriptions-item>
      <nz-descriptions-item nzTitle="Nhà phát hành">{{ item.issuer || 'N/A' }}</nz-descriptions-item>
      <nz-descriptions-item nzTitle="Có hiệu lực từ">
        {{ item.notBefore ? (item.notBefore | date : 'dd/MM/yyyy') : 'N/A' }}
      </nz-descriptions-item>
      <nz-descriptions-item nzTitle="Có hiệu lực đến">
        {{ item.notAfter ? (item.notAfter | date : 'dd/MM/yyyy') : 'N/A' }}
      </nz-descriptions-item>
      <nz-descriptions-item nzTitle="Nguồn">
        <nz-tag [nzColor]="item.source === 0 ? 'blue' : item.source === 1 ? 'green' : 'orange'">
          {{ item.source === 0 ? 'Hệ thống' : item.source === 1 ? 'Người dùng tải lên' : 'Import từ file' }}
        </nz-tag>
      </nz-descriptions-item>
      <nz-descriptions-item nzTitle="Thứ tự">{{ item.order || 'N/A' }}</nz-descriptions-item>
      <nz-descriptions-item nzTitle="Người dùng">{{ item.userId || 'N/A' }}</nz-descriptions-item>
      <nz-descriptions-item nzTitle="Ngày tạo">
        {{ item.createdDate ? (item.createdDate | date : 'dd/MM/yyyy HH:mm') : 'N/A' }}
      </nz-descriptions-item>
    </nz-descriptions>

    <!-- Form chỉ cho phép chỉnh sửa trạng thái -->
    <div *ngIf="isEdit" style="margin-top: 16px">
      <nz-divider nzText="Chỉnh sửa trạng thái"></nz-divider>
      <form nz-form [formGroup]="form" (ngSubmit)="save()">
        <nz-form-item>
          <nz-form-label [nzSm]="6" [nzXs]="24" nzFor="isActive">Trạng thái</nz-form-label>
          <nz-form-control [nzSm]="17" [nzXs]="24">
            <nz-switch formControlName="isActive" id="isActive"></nz-switch>
            <span style="margin-left: 8px">{{ form.get('isActive')?.value ? 'Đang hoạt động' : 'Ngừng hoạt động' }}</span>
          </nz-form-control>
        </nz-form-item>
      </form>
    </div>

    <!-- Thông báo về cách thêm chứng thư số -->
    <div *ngIf="isAdd" style="text-align: center; padding: 20px">
      <nz-alert
        nzType="info"
        nzMessage="Thông báo"
        nzDescription="Không thể thêm mới chứng thư số trực tiếp. Vui lòng sử dụng chức năng 'Quản lý tài khoản Visnam' để đồng bộ chứng thư số từ hệ thống Visnam."
        nzShowIcon
      ></nz-alert>
    </div>
  </ng-template>

  <ng-template #modalFooter>
    <!-- Nút chỉnh sửa chỉ hiển thị khi đang ở chế độ xem thông tin -->
    <button
      nz-button
      nzType="primary"
      class="btn-secondary"
      *ngIf="isInfo && btnEdit.visible && btnEdit.grandAccess"
      (click)="btnEdit.click($event)"
    >
      <i nz-icon nzType="edit" nzTheme="fill"></i>{{ btnEdit.title }}
    </button>

    <!-- Nút lưu chỉ hiển thị khi đang ở chế độ chỉnh sửa trạng thái -->
    <button
      nz-button
      nzType="primary"
      class="btn-primary"
      *ngIf="isEdit && btnSave.visible && btnSave.grandAccess"
      [nzLoading]="isLoading"
      (click)="btnSave.click($event)"
    >
      <i nz-icon nzType="save" nzTheme="fill"></i>{{ btnSave.title }}
    </button>

    <!-- Nút đóng luôn hiển thị -->
    <button
      nz-button
      nzType="default"
      class="btn-warning"
      *ngIf="btnCancel.visible && btnCancel.grandAccess"
      (click)="btnCancel.click($event)"
    >
      <i nz-icon nzType="close-circle" nzTheme="fill"></i>{{ btnCancel.title }}
    </button>
  </ng-template>
</nz-modal>
