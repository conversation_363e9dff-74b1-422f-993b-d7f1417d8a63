import { Component, EventEmitter, Inject, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, RequiredValidator, Validators } from '@angular/forms';
import ClassicEditor from '@ckeditor/ckeditor5-build-classic';
import { I18NService } from '@core';
import { ACLService } from '@delon/acl';
import { ALAIN_I18N_TOKEN } from '@delon/theme';
import { log } from '@delon/util';
import { ButtonModel } from '@model';
import { cleanForm, EVENT_TYPE, FORM_TYPE } from '@util';
import { NzMessageService } from 'ng-zorro-antd/message';
import { Subscription } from 'rxjs';
import { WorkflowApiService } from 'src/app/services/api/workflow-api.service';
import { WorkflowMapFunctionApiService } from 'src/app/services/api/workflow-map-function.service';

@Component({
  selector: 'app-workflow-map-function-item',
  templateUrl: './workflow-map-function-item.component.html',
  styleUrls: ['./workflow-map-function-item.component.less']
})
export class WorkflowMapFunctionItemComponent implements OnInit {
  @Input() type = 'add';
  @Input() item: any;
  @Input() isVisible = false;
  @Input() option: any;
  @Output() readonly eventEmmit = new EventEmitter<any>();

  form: FormGroup;

  tittle = '';

  isLoading = false;

  btnSave: ButtonModel;
  btnCancel: ButtonModel;
  btnEdit: ButtonModel;

  public Editor = ClassicEditor;

  config = {
    toolbar: {
      shouldNotGroupWhenFull: true
    }
  };

  lstQuyTrinh: any;

  constructor(
    private fb: FormBuilder,
    private messageService: NzMessageService,
    private workflowMapFunctionApiService: WorkflowMapFunctionApiService,
    private workflowApiService: WorkflowApiService,
    @Inject(ALAIN_I18N_TOKEN) private i18n: I18NService,
    private aclService: ACLService
  ) {
    this.btnSave = {
      title: this.i18n.fanyi('app.common.button.save'),
      visible: true,
      enable: true,
      grandAccess: true,
      click: ($event: any) => {
        this.save();
      }
    };
    this.btnCancel = {
      title: this.i18n.fanyi('app.common.button.close'),
      visible: true,
      enable: true,
      grandAccess: true,
      click: ($event: any) => {
        this.handleCancel();
      }
    };
    this.btnEdit = {
      title: this.i18n.fanyi('app.common.button.edit'),
      visible: true,
      enable: true,
      grandAccess: true,
      click: ($event: any) => {
        this.updateFormToEdit();
      }
    };
    this.form = this.fb.group({
      tenChucNang: [''],
      ghiChu: [''],
      tenQuyTrinh: [null, [Validators.required]],
      maPhanLoai: [''],
      isActive: [false],
      idPh: [null]
    });
  }

  onEditorReady(editor: any): void {
    log('Editor is ready to use!', editor);
  }

  handleCancel(): void {
    this.isVisible = false;
    this.eventEmmit.emit({ type: EVENT_TYPE.CLOSE });
  }

  ngOnInit(): void {
    this.initRightOfUser();
    this.getComboboxWorkflow();
  }

  initRightOfUser(): void {}

  //#region Update-form-type

  updateFormToEdit(): void {
    this.form.get('tenChucNang')?.disable();
    this.form.get('tenQuyTrinh')?.enable();
    this.form.get('ghiChu')?.enable();
    this.form.get('maPhanLoai')?.enable();
    this.form.get('isActive')?.enable();
    this.form.get('idPh')?.enable();
  }

  updateFormData(item: any): void {
    this.form.get('tenChucNang')?.setValue(item?.tenChucNang);
    this.form.get('tenQuyTrinh')?.setValue(item?.tenQuyTrinh);
    this.form.get('ghiChu')?.setValue(item?.ghiChu);
    this.form.get('maPhanLoai')?.setValue(item?.maPhanLoai);
    this.form.get('isActive')?.setValue(item?.isActive);
    this.form.get('idPh')?.setValue(item?.idPh);
  }

  clearFormData(item: any): void {}

  //#endregion Update-form-type

  public initData(data: any, type: any = null, option: any = {}): void {
    this.form.reset();
    this.isLoading = false;
    this.item = data;
    this.getDataInfo(data?.id);
    this.type = type;
    this.option = option;
  }

  closeModalReloadData(): void {
    this.isVisible = false;
    this.eventEmmit.emit({ type: EVENT_TYPE.SUCCESS });
  }

  getDataInfo(id: any): Subscription | undefined {
    this.isLoading = true;
    const rs = this.workflowMapFunctionApiService.getById(id).subscribe({
      next: (res: any) => {
        this.isLoading = false;
        if (res.data === null || res.data === undefined) {
          this.messageService.error(`${res.message}`);
          return;
        }
        this.item = res.data;
        this.updateFormToEdit();
        this.updateFormData(res.data);
      },
      error: (err: any) => {
        this.isLoading = false;
      },
      complete: () => {
        this.isLoading = false;
      }
    });
    return rs;
  }

  save(): Subscription | undefined {
    this.isLoading = true;
    console.log(this.form.getRawValue());
    const rs = this.workflowMapFunctionApiService.update(this.item.id, { ...this.form.getRawValue() }).subscribe({
      next: (res: any) => {
        this.isLoading = false;
        this.messageService.success(res?.message);
        this.closeModalReloadData();
      },
      error: (err: any) => {
        this.messageService.success(err?.error?.message);
        this.isLoading = false;
      }
    });
    return rs;
  }

  getComboboxWorkflow() {
    this.workflowApiService.getCombobox().subscribe({
      next: (res: any) => {
        this.lstQuyTrinh = res.data;
      },
      error: (err: any) => {
        this.messageService.success(err?.error?.message);
      }
    });
  }
}
