<nz-modal
  [(nzVisible)]="isVisible"
  [nzTitle]="modalTitle"
  [nzContent]="modalContent"
  [nzFooter]="modalFooter"
  nzMaskClosable="false"
  nzWidth="1000px"
  (nzOnCancel)="handleCancel()"
>
  <ng-template #modalTitle> {{ 'menu.workflow-function' | i18n }} </ng-template>

  <ng-template #modalContent>
    <form nz-form [formGroup]="form" (ngSubmit)="save()">
      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24" nzFor="tenChucNang">{{ 'workflow-map-function.table.ten-chuc-nang' | i18n }}</nz-form-label>
        <nz-form-control [nzSm]="17" [nzXs]="24">
          <input nz-input formControlName="tenChucNang" id="tenChucNang" />
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="tenQuyTrinh">{{
          'workflow-map-function.table.ten-quy-trinh' | i18n
        }}</nz-form-label>
        <nz-form-control [nzSm]="17" [nzXs]="24" nzErrorTip="{{ 'workflow-map-function.table.ten-quy-trinh.required' | i18n }}">
          <nz-select
            nzShowSearch
            id="tenQuyTrinh"
            formControlName="tenQuyTrinh"
            class="custom-select"
            name="tenQuyTrinh"
            nzPlaceHolder="{{ 'workflow-map-function.table.ten-quy-trinh.place-holder' | i18n }}"
          >
            <nz-option *ngFor="let p of lstQuyTrinh" [nzValue]="p.workflowName" [nzLabel]="p.workflowName"></nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24" nzFor="maPhanLoai">{{ 'workflow-map-function.table.ma-phan-loai' | i18n }}</nz-form-label>
        <nz-form-control [nzSm]="17" [nzXs]="24">
          <input nz-input formControlName="maPhanLoai" id="maPhanLoai" />
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24" nzFor="isActive">{{ 'workflow-map-function-item.table.is-active' | i18n }}</nz-form-label>
        <nz-form-control [nzSm]="17" [nzXs]="24">
          <label nz-checkbox formControlName="isActive" id="isActive"></label>
        </nz-form-control>
      </nz-form-item>
    </form>
  </ng-template>

  <ng-template #modalFooter>
    <button nz-button nzType="primary" class="btn-primary" [nzLoading]="isLoading" (click)="btnSave.click($event)">
      <i nz-icon nzType="save" nzTheme="fill"></i>{{ btnSave.title }}
    </button>
    <button nz-button nzType="default" class="btn-warning" (click)="btnCancel.click($event)">
      <i nz-icon nzType="close-circle" nzTheme="fill"></i>{{ btnCancel.title }}
    </button>
  </ng-template>
</nz-modal>
