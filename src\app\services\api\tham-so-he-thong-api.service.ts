import { Injectable } from '@angular/core';
import { _HttpClient } from '@delon/theme';
import { environment } from '@env/environment';
import { QueryFilerModel } from '@model';
import { thamSoHeThongRouter } from '@util';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ThamSoHeThongApiService {
  constructor(private http: _HttpClient) {}

  getFilter(model: QueryFilerModel): Observable<any> {
    return this.http.post(environment.api.baseUrl + thamSoHeThongRouter.getFilter, model);
  }

  getById(idPh: any, idThamSo: any): Observable<any> {
    return this.http.get(`${environment.api.baseUrl}${thamSoHeThongRouter.getById}${idPh}/${idThamSo}`);
  }

  update(idThamSo: any, model: any): Observable<any> {
    return this.http.put(environment.api.baseUrl + thamSoHeThongRouter.update + idThamSo, model);
  }
}
