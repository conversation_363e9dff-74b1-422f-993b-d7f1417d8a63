/* Component specific styles for ChungThuSoItem */
.btn-primary {
  margin-left: 8px;
}

.btn-secondary {
  margin-left: 8px;
}

.btn-warning {
  margin-left: 8px;
}

/* File input styling */
input[type="file"] {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  transition: border-color 0.3s;
}

input[type="file"]:hover {
  border-color: #40a9ff;
}

input[type="file"]:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  outline: 0;
}

/* Form validation styles */
.ant-form-item-has-error input[type="file"] {
  border-color: #ff4d4f;
}

.ant-form-item-has-error input[type="file"]:focus {
  border-color: #ff4d4f;
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
}

/* Success indicator */
nz-form-explain span {
  font-size: 12px;
  font-weight: 500;
}

/* Modal content spacing */
.ant-modal-body {
  max-height: 70vh;
  overflow-y: auto;
}

/* Form item spacing */
nz-form-item {
  margin-bottom: 16px;
}

/* Switch styling */
nz-switch {
  margin-right: 8px;
}
